"""
快速改进模块

实现最有效的改进技术，专注于能快速提升性能的关键点：
1. EXT_SOURCE深度组合特征
2. 更好的历史数据聚合
3. 优化的模型参数
4. 简单但有效的特征选择

目标：快速将AUC从0.785提升到0.79+

作者：Augment Agent
"""

import pandas as pd
import numpy as np
import logging
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)


class QuickImprovements:
    """快速改进实施器"""
    
    def __init__(self, data_dict):
        self.data = data_dict
    
    def create_ext_source_power_features(self, df):
        """创建EXT_SOURCE的强力组合特征"""
        logger.info("创建EXT_SOURCE强力组合特征")
        
        ext_sources = ['EXT_SOURCE_1', 'EXT_SOURCE_2', 'EXT_SOURCE_3']
        available_ext = [col for col in ext_sources if col in df.columns]
        
        if len(available_ext) < 2:
            logger.warning("EXT_SOURCE特征不足，跳过组合")
            return df
        
        # 基础统计特征
        df['EXT_SOURCE_MEAN'] = df[available_ext].mean(axis=1)
        df['EXT_SOURCE_STD'] = df[available_ext].std(axis=1)
        df['EXT_SOURCE_MAX'] = df[available_ext].max(axis=1)
        df['EXT_SOURCE_MIN'] = df[available_ext].min(axis=1)
        df['EXT_SOURCE_MEDIAN'] = df[available_ext].median(axis=1)
        
        # 高级组合特征
        if len(available_ext) >= 3:
            # 三元组合
            df['EXT_SOURCE_PRODUCT'] = df['EXT_SOURCE_1'] * df['EXT_SOURCE_2'] * df['EXT_SOURCE_3']
            df['EXT_SOURCE_WEIGHTED'] = (
                0.4 * df['EXT_SOURCE_1'].fillna(0) + 
                0.35 * df['EXT_SOURCE_2'].fillna(0) + 
                0.25 * df['EXT_SOURCE_3'].fillna(0)
            )
            
            # 排序特征
            ext_data = df[available_ext].values
            df['EXT_SOURCE_RANK_MEAN'] = np.mean(np.argsort(np.argsort(ext_data, axis=1), axis=1), axis=1)
            
        # 两两组合
        for i in range(len(available_ext)):
            for j in range(i+1, len(available_ext)):
                col1, col2 = available_ext[i], available_ext[j]
                df[f'{col1}_X_{col2}'] = df[col1] * df[col2]
                df[f'{col1}_DIV_{col2}'] = df[col1] / (df[col2] + 1e-8)
                df[f'{col1}_PLUS_{col2}'] = df[col1] + df[col2]
                df[f'{col1}_MINUS_{col2}'] = df[col1] - df[col2]
        
        # 与其他重要特征的交互
        if 'AMT_CREDIT' in df.columns:
            df['EXT_SOURCE_MEAN_X_CREDIT'] = df['EXT_SOURCE_MEAN'] * df['AMT_CREDIT']
        
        if 'AMT_INCOME_TOTAL' in df.columns:
            df['EXT_SOURCE_MEAN_X_INCOME'] = df['EXT_SOURCE_MEAN'] * df['AMT_INCOME_TOTAL']
        
        logger.info("EXT_SOURCE强力组合特征创建完成")
        return df
    
    def create_financial_ratio_features(self, df):
        """创建金融比率特征"""
        logger.info("创建金融比率特征")
        
        # 基础比率
        if all(col in df.columns for col in ['AMT_CREDIT', 'AMT_INCOME_TOTAL']):
            df['CREDIT_INCOME_RATIO'] = df['AMT_CREDIT'] / (df['AMT_INCOME_TOTAL'] + 1e-8)
            df['CREDIT_INCOME_RATIO_LOG'] = np.log1p(df['CREDIT_INCOME_RATIO'])
        
        if all(col in df.columns for col in ['AMT_ANNUITY', 'AMT_INCOME_TOTAL']):
            df['ANNUITY_INCOME_RATIO'] = df['AMT_ANNUITY'] / (df['AMT_INCOME_TOTAL'] + 1e-8)
            df['ANNUITY_INCOME_RATIO_LOG'] = np.log1p(df['ANNUITY_INCOME_RATIO'])
        
        if all(col in df.columns for col in ['AMT_CREDIT', 'AMT_GOODS_PRICE']):
            df['CREDIT_GOODS_RATIO'] = df['AMT_CREDIT'] / (df['AMT_GOODS_PRICE'] + 1e-8)
        
        # 年龄相关比率
        if 'DAYS_BIRTH' in df.columns:
            df['AGE_YEARS'] = -df['DAYS_BIRTH'] / 365.25
            
            if 'AMT_CREDIT' in df.columns:
                df['CREDIT_PER_AGE'] = df['AMT_CREDIT'] / (df['AGE_YEARS'] + 1e-8)
            
            if 'DAYS_EMPLOYED' in df.columns:
                df['EMPLOYMENT_YEARS'] = np.maximum(-df['DAYS_EMPLOYED'] / 365.25, 0)
                df['EMPLOYMENT_AGE_RATIO'] = df['EMPLOYMENT_YEARS'] / (df['AGE_YEARS'] + 1e-8)
        
        # 复合比率
        if all(col in df.columns for col in ['AMT_CREDIT', 'AMT_ANNUITY', 'AMT_INCOME_TOTAL']):
            df['TOTAL_DEBT_BURDEN'] = (df['AMT_CREDIT'] + df['AMT_ANNUITY']) / (df['AMT_INCOME_TOTAL'] + 1e-8)
        
        logger.info("金融比率特征创建完成")
        return df
    
    def create_bureau_enhanced_features(self, df):
        """创建增强的Bureau特征"""
        logger.info("创建增强的Bureau特征")
        
        if "bureau" not in self.data:
            return df
        
        bureau = self.data["bureau"].copy()
        
        # 活跃信贷分析
        active_bureau = bureau[bureau['CREDIT_ACTIVE'] == 'Active']
        if not active_bureau.empty:
            active_agg = active_bureau.groupby('SK_ID_CURR').agg({
                'AMT_CREDIT_SUM': ['sum', 'mean', 'count'],
                'AMT_CREDIT_SUM_DEBT': ['sum', 'mean'],
                'CREDIT_DAY_OVERDUE': ['max', 'mean'],
                'AMT_CREDIT_MAX_OVERDUE': ['max', 'sum']
            })
            active_agg.columns = ['BUREAU_ACTIVE_' + '_'.join(col) for col in active_agg.columns]
            df = df.merge(active_agg, on='SK_ID_CURR', how='left')
        
        # 已结清信贷分析
        closed_bureau = bureau[bureau['CREDIT_ACTIVE'] == 'Closed']
        if not closed_bureau.empty:
            closed_agg = closed_bureau.groupby('SK_ID_CURR').agg({
                'AMT_CREDIT_SUM': ['sum', 'mean', 'count'],
                'DAYS_CREDIT_ENDDATE': ['mean', 'min'],
                'CREDIT_DAY_OVERDUE': ['max', 'mean']
            })
            closed_agg.columns = ['BUREAU_CLOSED_' + '_'.join(col) for col in closed_agg.columns]
            df = df.merge(closed_agg, on='SK_ID_CURR', how='left')
        
        # 信贷类型多样性
        credit_type_counts = bureau.groupby('SK_ID_CURR')['CREDIT_TYPE'].nunique()
        credit_type_counts.name = 'BUREAU_CREDIT_TYPE_COUNT'
        df = df.merge(credit_type_counts.to_frame(), on='SK_ID_CURR', how='left')
        
        # 最近信贷活动
        recent_bureau = bureau[bureau['DAYS_CREDIT'] >= -365]  # 最近一年
        if not recent_bureau.empty:
            recent_agg = recent_bureau.groupby('SK_ID_CURR').agg({
                'AMT_CREDIT_SUM': ['sum', 'count'],
                'CREDIT_DAY_OVERDUE': ['max', 'mean']
            })
            recent_agg.columns = ['BUREAU_RECENT_' + '_'.join(col) for col in recent_agg.columns]
            df = df.merge(recent_agg, on='SK_ID_CURR', how='left')
        
        logger.info("增强的Bureau特征创建完成")
        return df
    
    def create_installments_behavior_features(self, df):
        """创建分期付款行为特征"""
        logger.info("创建分期付款行为特征")
        
        if "installments_payments" not in self.data:
            return df
        
        ins = self.data["installments_payments"].copy()
        
        # 还款行为分析
        ins['PAYMENT_RATIO'] = ins['AMT_PAYMENT'] / (ins['AMT_INSTALMENT'] + 1e-8)
        ins['PAYMENT_DELAY'] = ins['DAYS_ENTRY_PAYMENT'] - ins['DAYS_INSTALMENT']
        ins['IS_LATE'] = (ins['PAYMENT_DELAY'] > 0).astype(int)
        ins['IS_EARLY'] = (ins['PAYMENT_DELAY'] < -5).astype(int)  # 提前5天以上
        
        # 聚合特征
        payment_agg = ins.groupby('SK_ID_CURR').agg({
            'PAYMENT_RATIO': ['mean', 'std', 'min', 'max', lambda x: (x < 0.9).mean()],
            'PAYMENT_DELAY': ['mean', 'std', 'max', lambda x: (x > 30).mean()],
            'IS_LATE': ['mean', 'sum'],
            'IS_EARLY': ['mean', 'sum'],
            'AMT_PAYMENT': ['sum', 'mean', 'std'],
            'NUM_INSTALMENT_VERSION': ['max', 'mean']  # 分期计划变更次数
        })
        
        payment_agg.columns = ['INSTALL_' + '_'.join(col) if isinstance(col, tuple) else f'INSTALL_{col}' 
                              for col in payment_agg.columns]
        
        # 最近还款行为
        recent_ins = ins.sort_values('DAYS_INSTALMENT').groupby('SK_ID_CURR').tail(10)
        recent_agg = recent_ins.groupby('SK_ID_CURR').agg({
            'PAYMENT_RATIO': ['mean'],
            'IS_LATE': ['mean'],
            'PAYMENT_DELAY': ['mean']
        })
        recent_agg.columns = ['INSTALL_RECENT_' + '_'.join(col) for col in recent_agg.columns]
        
        # 合并特征
        df = df.merge(payment_agg, on='SK_ID_CURR', how='left')
        df = df.merge(recent_agg, on='SK_ID_CURR', how='left')
        
        logger.info("分期付款行为特征创建完成")
        return df
    
    def select_best_features(self, X, y, k=500):
        """选择最佳特征"""
        logger.info(f"选择最佳{k}个特征")
        
        # 使用F统计量选择特征
        selector = SelectKBest(score_func=f_classif, k=min(k, X.shape[1]))
        X_selected = selector.fit_transform(X, y)
        
        # 获取选中的特征名
        selected_features = X.columns[selector.get_support()].tolist()
        
        logger.info(f"从{X.shape[1]}个特征中选择了{len(selected_features)}个")
        return X[selected_features], selected_features
    
    def get_optimized_params(self):
        """获取优化后的模型参数"""
        return {
            'xgboost_params': {
                'max_depth': 7,
                'learning_rate': 0.015,  # 更小的学习率
                'n_estimators': 4000,    # 更多轮次
                'subsample': 0.85,
                'colsample_bytree': 0.85,
                'min_child_weight': 5,
                'reg_alpha': 0.3,
                'reg_lambda': 0.3,
                'gamma': 0.1,
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'random_state': 42,
                'n_jobs': -1,
            },
            'lightgbm_params': {
                'objective': 'binary',
                'metric': 'auc',
                'boosting_type': 'gbdt',
                'num_leaves': 45,        # 稍微增加复杂度
                'learning_rate': 0.015,  # 更小的学习率
                'feature_fraction': 0.85,
                'bagging_fraction': 0.85,
                'bagging_freq': 5,
                'min_child_samples': 30,
                'reg_alpha': 0.3,
                'reg_lambda': 0.3,
                'min_split_gain': 0.02,
                'verbose': -1,
                'random_state': 42,
                'n_estimators': 4000,    # 更多轮次
                'n_jobs': -1,
            }
        }
    
    def apply_all_improvements(self, df):
        """应用所有快速改进"""
        logger.info("应用所有快速改进")
        
        original_features = df.shape[1]
        
        # 1. EXT_SOURCE强力组合
        df = self.create_ext_source_power_features(df)
        
        # 2. 金融比率特征
        df = self.create_financial_ratio_features(df)
        
        # 3. 增强的Bureau特征
        df = self.create_bureau_enhanced_features(df)
        
        # 4. 分期付款行为特征
        df = self.create_installments_behavior_features(df)
        
        new_features = df.shape[1] - original_features
        logger.info(f"快速改进完成，新增{new_features}个特征，总特征数: {df.shape[1]}")
        
        return df
