"""
多模型集成优化器

实现大规模多模型集成，包括：
1. XGBoost、LightGBM、CatBoost、ExtraTrees
2. 神经网络模型
3. 多层堆叠（Stacking）
4. 动态权重融合
5. 贝叶斯模型平均

目标：从0.789提升到0.8+

作者：Augment Agent
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.linear_model import LogisticRegression, Ridge
from sklearn.ensemble import ExtraTreesClassifier, RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import roc_auc_score
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
try:
    from catboost import CatBoostClassifier
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

import logging
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class MultiModelEnsemble:
    """多模型集成器"""
    
    def __init__(self, n_folds=5, random_state=42):
        self.n_folds = n_folds
        self.random_state = random_state
        self.models = []
        self.meta_models = []
        self.oof_predictions = None
        self.test_predictions = None
        self.feature_importances = None
        
    def get_base_model_configs(self):
        """获取基础模型配置"""
        configs = []
        
        # XGBoost变种（多个配置）
        xgb_configs = [
            {
                'name': 'xgb_conservative',
                'model': XGBClassifier,
                'params': {
                    'objective': 'binary:logistic',
                    'eval_metric': 'auc',
                    'max_depth': 6,
                    'learning_rate': 0.01,
                    'n_estimators': 5000,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'min_child_weight': 5,
                    'reg_alpha': 0.3,
                    'reg_lambda': 0.3,
                    'random_state': 42,
                    'n_jobs': -1,
                }
            },
            {
                'name': 'xgb_aggressive',
                'model': XGBClassifier,
                'params': {
                    'objective': 'binary:logistic',
                    'eval_metric': 'auc',
                    'max_depth': 8,
                    'learning_rate': 0.02,
                    'n_estimators': 4000,
                    'subsample': 0.7,
                    'colsample_bytree': 0.7,
                    'min_child_weight': 1,
                    'reg_alpha': 0.1,
                    'reg_lambda': 0.1,
                    'random_state': 123,
                    'n_jobs': -1,
                }
            },
            {
                'name': 'xgb_deep',
                'model': XGBClassifier,
                'params': {
                    'objective': 'binary:logistic',
                    'eval_metric': 'auc',
                    'max_depth': 10,
                    'learning_rate': 0.005,
                    'n_estimators': 8000,
                    'subsample': 0.85,
                    'colsample_bytree': 0.85,
                    'min_child_weight': 3,
                    'reg_alpha': 0.2,
                    'reg_lambda': 0.2,
                    'random_state': 456,
                    'n_jobs': -1,
                }
            }
        ]
        
        # LightGBM变种
        lgb_configs = [
            {
                'name': 'lgb_conservative',
                'model': LGBMClassifier,
                'params': {
                    'objective': 'binary',
                    'metric': 'auc',
                    'boosting_type': 'gbdt',
                    'num_leaves': 31,
                    'learning_rate': 0.01,
                    'feature_fraction': 0.8,
                    'bagging_fraction': 0.8,
                    'bagging_freq': 5,
                    'min_child_samples': 30,
                    'reg_alpha': 0.3,
                    'reg_lambda': 0.3,
                    'verbose': -1,
                    'random_state': 42,
                    'n_estimators': 5000,
                    'n_jobs': -1,
                }
            },
            {
                'name': 'lgb_aggressive',
                'model': LGBMClassifier,
                'params': {
                    'objective': 'binary',
                    'metric': 'auc',
                    'boosting_type': 'gbdt',
                    'num_leaves': 63,
                    'learning_rate': 0.02,
                    'feature_fraction': 0.7,
                    'bagging_fraction': 0.7,
                    'bagging_freq': 5,
                    'min_child_samples': 10,
                    'reg_alpha': 0.1,
                    'reg_lambda': 0.1,
                    'verbose': -1,
                    'random_state': 123,
                    'n_estimators': 4000,
                    'n_jobs': -1,
                }
            },
            {
                'name': 'lgb_dart',
                'model': LGBMClassifier,
                'params': {
                    'objective': 'binary',
                    'metric': 'auc',
                    'boosting_type': 'dart',
                    'num_leaves': 40,
                    'learning_rate': 0.015,
                    'feature_fraction': 0.85,
                    'bagging_fraction': 0.85,
                    'bagging_freq': 5,
                    'min_child_samples': 20,
                    'reg_alpha': 0.2,
                    'reg_lambda': 0.2,
                    'drop_rate': 0.1,
                    'verbose': -1,
                    'random_state': 456,
                    'n_estimators': 3000,
                    'n_jobs': -1,
                }
            }
        ]
        
        # CatBoost配置
        if CATBOOST_AVAILABLE:
            cat_configs = [
                {
                    'name': 'cat_default',
                    'model': CatBoostClassifier,
                    'params': {
                        'objective': 'Logloss',
                        'eval_metric': 'AUC',
                        'depth': 6,
                        'learning_rate': 0.02,
                        'iterations': 4000,
                        'l2_leaf_reg': 3,
                        'bagging_temperature': 1,
                        'random_strength': 1,
                        'one_hot_max_size': 2,
                        'leaf_estimation_method': 'Newton',
                        'random_state': 42,
                        'verbose': False,
                    }
                },
                {
                    'name': 'cat_deep',
                    'model': CatBoostClassifier,
                    'params': {
                        'objective': 'Logloss',
                        'eval_metric': 'AUC',
                        'depth': 8,
                        'learning_rate': 0.01,
                        'iterations': 6000,
                        'l2_leaf_reg': 5,
                        'bagging_temperature': 0.8,
                        'random_strength': 0.8,
                        'one_hot_max_size': 2,
                        'leaf_estimation_method': 'Newton',
                        'random_state': 123,
                        'verbose': False,
                    }
                }
            ]
            configs.extend(cat_configs)
        
        # ExtraTrees配置
        et_configs = [
            {
                'name': 'et_default',
                'model': ExtraTreesClassifier,
                'params': {
                    'n_estimators': 1000,
                    'max_depth': 10,
                    'min_samples_split': 10,
                    'min_samples_leaf': 5,
                    'max_features': 'sqrt',
                    'bootstrap': True,
                    'random_state': 42,
                    'n_jobs': -1,
                }
            },
            {
                'name': 'et_deep',
                'model': ExtraTreesClassifier,
                'params': {
                    'n_estimators': 1500,
                    'max_depth': 15,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'max_features': 0.8,
                    'bootstrap': True,
                    'random_state': 123,
                    'n_jobs': -1,
                }
            }
        ]
        
        # RandomForest配置
        rf_configs = [
            {
                'name': 'rf_default',
                'model': RandomForestClassifier,
                'params': {
                    'n_estimators': 1000,
                    'max_depth': 12,
                    'min_samples_split': 8,
                    'min_samples_leaf': 4,
                    'max_features': 'sqrt',
                    'bootstrap': True,
                    'random_state': 42,
                    'n_jobs': -1,
                }
            }
        ]
        
        # 神经网络配置
        nn_configs = [
            {
                'name': 'mlp_default',
                'model': MLPClassifier,
                'params': {
                    'hidden_layer_sizes': (200, 100, 50),
                    'activation': 'relu',
                    'solver': 'adam',
                    'alpha': 0.001,
                    'learning_rate': 'adaptive',
                    'learning_rate_init': 0.001,
                    'max_iter': 500,
                    'random_state': 42,
                }
            }
        ]
        
        # 合并所有配置
        configs.extend(xgb_configs)
        configs.extend(lgb_configs)
        configs.extend(et_configs)
        configs.extend(rf_configs)
        configs.extend(nn_configs)
        
        return configs
    
    def train_base_models(self, X, y, test_X):
        """训练基础模型"""
        logger.info("开始训练多模型集成")
        
        model_configs = self.get_base_model_configs()
        n_models = len(model_configs)
        
        # 设置交叉验证
        kf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
        
        # 初始化预测矩阵
        oof_preds = np.zeros((X.shape[0], n_models))
        test_preds = np.zeros((test_X.shape[0], n_models))
        feature_importance_df = pd.DataFrame()
        
        # 训练每个模型
        for model_idx, model_config in enumerate(model_configs):
            logger.info(f"训练模型 {model_idx+1}/{n_models}: {model_config['name']}")
            
            model_oof = np.zeros(X.shape[0])
            model_test = np.zeros(test_X.shape[0])
            
            # 交叉验证训练
            for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
                logger.info(f"{model_config['name']} - 折 {fold+1}/{self.n_folds}")
                
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # 创建并训练模型
                model = model_config['model'](**model_config['params'])
                
                # 特殊处理不同类型的模型
                try:
                    if 'lgb' in model_config['name']:
                        # LightGBM early stopping
                        try:
                            from lightgbm import early_stopping
                            model.fit(
                                X_train, y_train,
                                eval_set=[(X_val, y_val)],
                                callbacks=[early_stopping(200)]
                            )
                        except:
                            # 备选方案：不使用early stopping
                            model.fit(X_train, y_train)

                    elif 'xgb' in model_config['name']:
                        # XGBoost兼容性处理
                        try:
                            # 尝试新版本API
                            import xgboost as xgb
                            if hasattr(xgb, 'callback'):
                                model.fit(
                                    X_train, y_train,
                                    eval_set=[(X_val, y_val)],
                                    callbacks=[xgb.callback.EarlyStopping(rounds=200)],
                                    verbose=False
                                )
                            else:
                                # 旧版本API
                                model.fit(
                                    X_train, y_train,
                                    eval_set=[(X_val, y_val)],
                                    early_stopping_rounds=200,
                                    verbose=False
                                )
                        except Exception as xgb_error:
                            logger.warning(f"XGBoost early stopping失败: {xgb_error}")
                            # 备选方案：不使用early stopping
                            model.fit(X_train, y_train)

                    elif 'cat' in model_config['name'] and CATBOOST_AVAILABLE:
                        try:
                            model.fit(
                                X_train, y_train,
                                eval_set=(X_val, y_val),
                                early_stopping_rounds=200,
                                verbose=False
                            )
                        except:
                            model.fit(X_train, y_train)
                    else:
                        model.fit(X_train, y_train)

                except Exception as e:
                    logger.warning(f"模型训练失败: {model_config['name']}, 错误: {e}")
                    # 使用简单训练作为备选
                    try:
                        model.fit(X_train, y_train)
                    except Exception as e2:
                        logger.error(f"简单训练也失败: {model_config['name']}, 错误: {e2}")
                        continue
                
                # 生成预测
                if hasattr(model, 'predict_proba'):
                    model_oof[val_idx] = model.predict_proba(X_val)[:, 1]
                    model_test += model.predict_proba(test_X)[:, 1] / self.n_folds
                else:
                    model_oof[val_idx] = model.predict(X_val)
                    model_test += model.predict(test_X) / self.n_folds
                
                # 计算验证集AUC
                fold_auc = roc_auc_score(y_val, model_oof[val_idx])
                logger.info(f"{model_config['name']} - 折 {fold+1} AUC: {fold_auc:.6f}")
                
                # 记录特征重要性
                if hasattr(model, 'feature_importances_'):
                    fold_importance = pd.DataFrame()
                    fold_importance['feature'] = X.columns
                    fold_importance['importance'] = model.feature_importances_
                    fold_importance['model'] = model_config['name']
                    fold_importance['fold'] = fold + 1
                    feature_importance_df = pd.concat([feature_importance_df, fold_importance], axis=0)
            
            # 保存模型预测
            oof_preds[:, model_idx] = model_oof
            test_preds[:, model_idx] = model_test
            
            # 计算模型整体AUC
            model_auc = roc_auc_score(y, model_oof)
            logger.info(f"{model_config['name']} 整体AUC: {model_auc:.6f}")
        
        # 保存结果
        self.oof_predictions = oof_preds
        self.test_predictions = test_preds
        if not feature_importance_df.empty:
            self.feature_importances = (
                feature_importance_df.groupby('feature')['importance']
                .mean()
                .sort_values(ascending=False)
            )
        
        return oof_preds, test_preds

    def train_stacking_models(self, oof_preds, y, test_preds):
        """训练堆叠模型"""
        logger.info("开始训练堆叠模型")

        # 第一层元模型配置
        meta_configs = [
            {
                'name': 'meta_logistic',
                'model': LogisticRegression,
                'params': {
                    'C': 0.1,
                    'penalty': 'l2',
                    'solver': 'liblinear',
                    'random_state': self.random_state,
                    'max_iter': 1000
                }
            },
            {
                'name': 'meta_ridge',
                'model': Ridge,
                'params': {
                    'alpha': 1.0,
                    'random_state': self.random_state
                }
            },
            {
                'name': 'meta_lgb',
                'model': LGBMClassifier,
                'params': {
                    'objective': 'binary',
                    'metric': 'auc',
                    'num_leaves': 10,
                    'learning_rate': 0.1,
                    'n_estimators': 200,
                    'verbose': -1,
                    'random_state': self.random_state
                }
            }
        ]

        # 训练元模型
        meta_oof_preds = []
        meta_test_preds = []

        kf = StratifiedKFold(n_splits=3, shuffle=True, random_state=self.random_state)

        for meta_config in meta_configs:
            logger.info(f"训练元模型: {meta_config['name']}")

            meta_oof = np.zeros(len(y))
            meta_test = np.zeros(test_preds.shape[0])

            for fold, (train_idx, val_idx) in enumerate(kf.split(oof_preds, y)):
                X_meta_train = oof_preds[train_idx]
                X_meta_val = oof_preds[val_idx]
                y_meta_train = y.iloc[train_idx]
                y_meta_val = y.iloc[val_idx]

                # 训练元模型
                meta_model = meta_config['model'](**meta_config['params'])
                meta_model.fit(X_meta_train, y_meta_train)

                # 预测
                if hasattr(meta_model, 'predict_proba'):
                    meta_oof[val_idx] = meta_model.predict_proba(X_meta_val)[:, 1]
                    meta_test += meta_model.predict_proba(test_preds)[:, 1] / 3
                else:
                    meta_oof[val_idx] = meta_model.predict(X_meta_val)
                    meta_test += meta_model.predict(test_preds) / 3

            meta_oof_preds.append(meta_oof)
            meta_test_preds.append(meta_test)

            # 计算元模型AUC
            meta_auc = roc_auc_score(y, meta_oof)
            logger.info(f"{meta_config['name']} 元模型AUC: {meta_auc:.6f}")

        return np.column_stack(meta_oof_preds), np.column_stack(meta_test_preds)

    def bayesian_model_averaging(self, oof_preds, test_preds, y):
        """贝叶斯模型平均"""
        logger.info("执行贝叶斯模型平均")

        # 计算每个模型的性能权重
        model_weights = []
        for i in range(oof_preds.shape[1]):
            auc = roc_auc_score(y, oof_preds[:, i])
            # 使用指数权重，性能越好权重越大
            weight = np.exp(auc * 15)  # 放大差异
            model_weights.append(weight)

        # 归一化权重
        model_weights = np.array(model_weights)
        model_weights = model_weights / model_weights.sum()

        # 加权融合
        bma_oof = np.average(oof_preds, axis=1, weights=model_weights)
        bma_test = np.average(test_preds, axis=1, weights=model_weights)

        bma_auc = roc_auc_score(y, bma_oof)
        logger.info(f"贝叶斯模型平均AUC: {bma_auc:.6f}")
        logger.info(f"模型权重分布: {dict(enumerate(model_weights))}")

        return bma_oof, bma_test, model_weights

    def rank_averaging(self, oof_preds, test_preds, y):
        """排名平均"""
        logger.info("执行排名平均")

        # 将预测转换为排名
        oof_ranks = np.zeros_like(oof_preds)
        test_ranks = np.zeros_like(test_preds)

        for i in range(oof_preds.shape[1]):
            oof_ranks[:, i] = pd.Series(oof_preds[:, i]).rank(pct=True)
            test_ranks[:, i] = pd.Series(test_preds[:, i]).rank(pct=True)

        # 平均排名
        rank_oof = np.mean(oof_ranks, axis=1)
        rank_test = np.mean(test_ranks, axis=1)

        rank_auc = roc_auc_score(y, rank_oof)
        logger.info(f"排名平均AUC: {rank_auc:.6f}")

        return rank_oof, rank_test

    def ensemble_all_methods(self, X, y, test_X):
        """集成所有方法"""
        logger.info("开始全方法集成")

        # 1. 训练基础模型
        oof_preds, test_preds = self.train_base_models(X, y, test_X)

        # 2. 训练堆叠模型
        meta_oof_preds, meta_test_preds = self.train_stacking_models(oof_preds, y, test_preds)

        # 3. 贝叶斯模型平均
        bma_oof, bma_test, model_weights = self.bayesian_model_averaging(oof_preds, test_preds, y)

        # 4. 排名平均
        rank_oof, rank_test = self.rank_averaging(oof_preds, test_preds, y)

        # 5. 简单平均
        simple_oof = np.mean(oof_preds, axis=1)
        simple_test = np.mean(test_preds, axis=1)
        simple_auc = roc_auc_score(y, simple_oof)
        logger.info(f"简单平均AUC: {simple_auc:.6f}")

        # 6. 最终融合（融合所有方法的结果）
        final_methods = {
            'bma': (bma_oof, bma_test),
            'rank': (rank_oof, rank_test),
            'simple': (simple_oof, simple_test),
            'meta_avg': (np.mean(meta_oof_preds, axis=1), np.mean(meta_test_preds, axis=1))
        }

        # 选择最佳方法或加权融合
        best_method = 'bma'  # 默认使用贝叶斯平均
        best_auc = roc_auc_score(y, final_methods[best_method][0])

        for method_name, (method_oof, method_test) in final_methods.items():
            method_auc = roc_auc_score(y, method_oof)
            logger.info(f"{method_name} AUC: {method_auc:.6f}")
            if method_auc > best_auc:
                best_method = method_name
                best_auc = method_auc

        logger.info(f"最佳方法: {best_method}, AUC: {best_auc:.6f}")

        final_oof, final_test = final_methods[best_method]

        return final_oof, final_test, {
            'base_predictions': (oof_preds, test_preds),
            'meta_predictions': (meta_oof_preds, meta_test_preds),
            'method_results': final_methods,
            'best_method': best_method,
            'model_weights': model_weights
        }
