"""
Home Credit Default Risk - 增强版主程序

整合所有改进技术的完整解决方案：
1. 高级特征工程
2. 超参数优化
3. 增强版集成模型
4. 性能分析

目标：将AUC从0.78提升到0.8+

作者：Augment Agent
日期：2025-07-11
"""

import pandas as pd
import numpy as np
import yaml
import logging
import os
import argparse
from datetime import datetime

from data_loader import DataLoader
from preprocessor import Preprocessor
from feature_engineering import FeatureEngineer
from advanced_feature_engineering import AdvancedFeatureEngineer
from enhanced_ensemble_trainer import EnhancedEnsembleTrainer
from hyperparameter_optimizer import HyperparameterOptimizer
from performance_analysis import PerformanceAnalyzer
from sklearn.metrics import roc_auc_score


def setup_logging(log_dir="logs"):
    """设置日志记录"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"enhanced_pipeline_{timestamp}.log")

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_file), logging.StreamHandler()],
    )

    return logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Enhanced Home Credit Default Risk Pipeline"
    )
    parser.add_argument(
        "--config", type=str, default="config.yaml", help="配置文件路径"
    )
    parser.add_argument(
        "--output", type=str, default="enhanced_submission.csv", help="提交文件输出路径"
    )
    parser.add_argument("--log_dir", type=str, default="logs", help="日志目录")
    parser.add_argument(
        "--optimize_hyperparams", action="store_true", help="是否进行超参数优化"
    )
    parser.add_argument(
        "--n_trials", type=int, default=50, help="超参数优化试验次数"
    )
    parser.add_argument(
        "--skip_analysis", action="store_true", help="跳过性能分析"
    )
    return parser.parse_args()


def main():
    """主函数"""
    # 解析参数
    args = parse_args()

    # 设置日志
    logger = setup_logging(args.log_dir)
    logger.info("开始执行Enhanced Home Credit Default Risk Pipeline")

    # 加载配置
    with open(args.config, "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)

    try:
        # 1. 加载数据
        logger.info("步骤1: 加载数据")
        data_loader = DataLoader(config)
        data_dict = data_loader.load_data()
        train_df, test_df = data_loader.get_train_test_data()

        # 2. 数据预处理
        logger.info("步骤2: 数据预处理")
        preprocessor = Preprocessor()
        train_df_processed = preprocessor.preprocess(train_df, is_train=True)
        test_df_processed = preprocessor.preprocess(test_df, is_train=False)

        # 3. 基础特征工程
        logger.info("步骤3: 基础特征工程")
        feature_engineer = FeatureEngineer(data_dict)
        train_df_featured = feature_engineer.engineer_features(train_df_processed)
        test_df_featured = feature_engineer.engineer_features(test_df_processed)

        # 4. 高级特征工程
        logger.info("步骤4: 高级特征工程")
        advanced_engineer = AdvancedFeatureEngineer(data_dict)
        train_df_advanced = advanced_engineer.engineer_advanced_features(train_df_featured)
        test_df_advanced = advanced_engineer.engineer_advanced_features(test_df_featured)

        # 确保训练集和测试集有相同的列
        common_cols = [
            col for col in train_df_advanced.columns if col in test_df_advanced.columns
        ]
        common_cols = [col for col in common_cols if col not in ["TARGET", "SK_ID_CURR"]]

        logger.info(f"最终特征数量: {len(common_cols)}")

        # 准备训练数据
        X = train_df_advanced[common_cols]
        y = train_df_advanced["TARGET"]
        test_X = test_df_advanced[common_cols]
        test_ids = test_df_advanced["SK_ID_CURR"]

        # 清理特征名
        def clean_feature_names(df):
            df = df.copy()
            import re
            df.columns = [re.sub(r"[^a-zA-Z0-9_]", "_", col) for col in df.columns]
            return df

        X = clean_feature_names(X)
        test_X = clean_feature_names(test_X)

        # 最终数据清理
        logger.info("最终数据清理")

        # 处理非数值列
        for col in X.columns:
            if X[col].dtype == 'object':
                logger.warning(f"发现非数值列 {col}，尝试转换为数值")
                # 尝试转换为数值，失败则用众数填充
                try:
                    X[col] = pd.to_numeric(X[col], errors='coerce')
                    test_X[col] = pd.to_numeric(test_X[col], errors='coerce')
                except:
                    # 如果转换失败，删除该列
                    logger.warning(f"删除无法转换的列: {col}")
                    X = X.drop(columns=[col])
                    test_X = test_X.drop(columns=[col])
                    continue

        # 处理无穷大值
        X = X.replace([np.inf, -np.inf], np.nan)
        test_X = test_X.replace([np.inf, -np.inf], np.nan)

        # 处理极值
        for col in X.select_dtypes(include=[np.number]).columns:
            X[col] = X[col].clip(lower=-1e10, upper=1e10)
            test_X[col] = test_X[col].clip(lower=-1e10, upper=1e10)

        # 最终检查数据类型
        logger.info(f"最终数据形状: X={X.shape}, test_X={test_X.shape}")
        logger.info(f"数据类型分布: {X.dtypes.value_counts().to_dict()}")

        # 5. 超参数优化（可选）
        if args.optimize_hyperparams:
            logger.info("步骤5: 超参数优化")
            # 使用部分数据进行快速优化
            sample_size = min(50000, len(X))
            sample_idx = np.random.choice(len(X), sample_size, replace=False)
            X_sample = X.iloc[sample_idx]
            y_sample = y.iloc[sample_idx]

            optimizer = HyperparameterOptimizer(
                X_sample, y_sample, n_trials=args.n_trials, cv_folds=3
            )
            optimization_results = optimizer.optimize_all_models()
            logger.info(f"超参数优化结果: {optimization_results}")

        # 6. 增强版集成模型训练
        logger.info("步骤6: 增强版集成模型训练")
        enhanced_trainer = EnhancedEnsembleTrainer(config, use_optimized_params=args.optimize_hyperparams)
        
        # 训练基模型
        oof_preds, test_preds = enhanced_trainer.train_base_models(X, y, test_X)
        
        # 训练元模型
        meta_oof_preds = enhanced_trainer.train_meta_models(oof_preds, y)
        
        # 动态加权融合
        final_oof, final_test, model_weights = enhanced_trainer.dynamic_weighted_blend(
            oof_preds, test_preds, y
        )

        # 7. 性能分析
        if not args.skip_analysis:
            logger.info("步骤7: 性能分析")
            analyzer = PerformanceAnalyzer()
            
            # 分析特征重要性
            if enhanced_trainer.feature_importances is not None:
                analyzer.analyze_feature_importance(enhanced_trainer.feature_importances)
            
            # 分析模型性能
            analyzer.analyze_model_performance(oof_preds, y)
            
            # 分析数据质量
            analyzer.analyze_data_quality(X, y)
            
            # 生成改进建议
            recommendations = analyzer.generate_improvement_recommendations()
            
            # 保存分析报告
            analyzer.save_analysis_report("enhanced_performance_analysis.txt")

        # 8. 生成提交文件
        logger.info("步骤8: 生成提交文件")
        submission = enhanced_trainer.generate_submission(final_test, test_ids, args.output)

        # 输出最终结果
        final_auc = roc_auc_score(y, final_oof)
        logger.info(f"最终交叉验证AUC: {final_auc:.6f}")
        
        if enhanced_trainer.feature_importances is not None:
            top_features = enhanced_trainer.feature_importances.head(20)
            logger.info("Top 20 重要特征:")
            for feature, importance in top_features.items():
                logger.info(f"  {feature}: {importance:.6f}")

        logger.info("Enhanced Pipeline执行完成")
        logger.info(f"提交文件已保存到: {args.output}")

    except Exception as e:
        logger.error(f"Pipeline执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    main()
