"""
性能分析模块

分析当前模型的性能瓶颈，识别改进机会：
1. 特征重要性分析
2. 模型性能对比
3. 数据质量评估
4. 交叉验证稳定性分析

作者：Augment Agent
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_auc_score, roc_curve
from sklearn.model_selection import cross_val_score
import logging

logger = logging.getLogger(__name__)


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.analysis_results = {}
    
    def analyze_feature_importance(self, feature_importance_df, top_n=50):
        """分析特征重要性"""
        logger.info("分析特征重要性")
        
        # 获取top特征
        top_features = feature_importance_df.head(top_n)
        
        # 分析特征类型分布
        feature_types = {
            'bureau': len([f for f in top_features.index if 'bureau' in f.lower()]),
            'previous': len([f for f in top_features.index if 'prev' in f.lower()]),
            'credit_card': len([f for f in top_features.index if 'credit_card' in f.lower()]),
            'pos_cash': len([f for f in top_features.index if 'pos_cash' in f.lower()]),
            'installments': len([f for f in top_features.index if 'payment' in f.lower()]),
            'ext_source': len([f for f in top_features.index if 'ext_source' in f.lower()]),
            'domain': len([f for f in top_features.index if any(x in f.lower() for x in ['debt', 'income', 'age', 'employment'])]),
            'interaction': len([f for f in top_features.index if any(x in f.lower() for x in ['_x_', '_div_', '_minus_'])])
        }
        
        self.analysis_results['feature_types'] = feature_types
        self.analysis_results['top_features'] = top_features
        
        logger.info(f"特征类型分布: {feature_types}")
        return feature_types, top_features
    
    def analyze_model_performance(self, oof_predictions, y_true):
        """分析模型性能"""
        logger.info("分析模型性能")
        
        # 计算每个模型的AUC
        model_aucs = []
        for i in range(oof_predictions.shape[1]):
            auc = roc_auc_score(y_true, oof_predictions[:, i])
            model_aucs.append(auc)
        
        # 分析模型多样性
        correlations = np.corrcoef(oof_predictions.T)
        avg_correlation = np.mean(correlations[np.triu_indices_from(correlations, k=1)])
        
        # 分析集成效果
        ensemble_pred = np.mean(oof_predictions, axis=1)
        ensemble_auc = roc_auc_score(y_true, ensemble_pred)
        
        performance_stats = {
            'individual_aucs': model_aucs,
            'avg_individual_auc': np.mean(model_aucs),
            'ensemble_auc': ensemble_auc,
            'ensemble_improvement': ensemble_auc - np.mean(model_aucs),
            'avg_correlation': avg_correlation,
            'diversity_score': 1 - avg_correlation
        }
        
        self.analysis_results['performance'] = performance_stats
        
        logger.info(f"平均单模型AUC: {np.mean(model_aucs):.6f}")
        logger.info(f"集成AUC: {ensemble_auc:.6f}")
        logger.info(f"集成提升: {ensemble_auc - np.mean(model_aucs):.6f}")
        logger.info(f"模型多样性分数: {1 - avg_correlation:.6f}")
        
        return performance_stats
    
    def analyze_data_quality(self, X, y):
        """分析数据质量"""
        logger.info("分析数据质量")
        
        # 缺失值分析
        missing_stats = X.isnull().sum().sort_values(ascending=False)
        high_missing_features = missing_stats[missing_stats > len(X) * 0.5]
        
        # 特征分布分析
        numeric_features = X.select_dtypes(include=[np.number]).columns
        skewness = X[numeric_features].skew().abs().sort_values(ascending=False)
        high_skew_features = skewness[skewness > 2]
        
        # 常数特征检测
        constant_features = []
        for col in X.columns:
            if X[col].nunique() <= 1:
                constant_features.append(col)
        
        # 重复特征检测
        duplicate_features = []
        for i, col1 in enumerate(X.columns):
            for col2 in X.columns[i+1:]:
                if X[col1].equals(X[col2]):
                    duplicate_features.append((col1, col2))
        
        data_quality = {
            'total_features': len(X.columns),
            'high_missing_count': len(high_missing_features),
            'high_skew_count': len(high_skew_features),
            'constant_features': constant_features,
            'duplicate_features': duplicate_features,
            'missing_stats': missing_stats.head(20),
            'skewness_stats': skewness.head(20)
        }
        
        self.analysis_results['data_quality'] = data_quality
        
        logger.info(f"总特征数: {len(X.columns)}")
        logger.info(f"高缺失特征数: {len(high_missing_features)}")
        logger.info(f"高偏斜特征数: {len(high_skew_features)}")
        logger.info(f"常数特征数: {len(constant_features)}")
        logger.info(f"重复特征对数: {len(duplicate_features)}")
        
        return data_quality
    
    def analyze_cv_stability(self, cv_scores):
        """分析交叉验证稳定性"""
        logger.info("分析交叉验证稳定性")
        
        stability_stats = {
            'mean_score': np.mean(cv_scores),
            'std_score': np.std(cv_scores),
            'min_score': np.min(cv_scores),
            'max_score': np.max(cv_scores),
            'score_range': np.max(cv_scores) - np.min(cv_scores),
            'cv_coefficient': np.std(cv_scores) / np.mean(cv_scores)
        }
        
        self.analysis_results['cv_stability'] = stability_stats
        
        logger.info(f"CV平均分数: {stability_stats['mean_score']:.6f}")
        logger.info(f"CV标准差: {stability_stats['std_score']:.6f}")
        logger.info(f"CV变异系数: {stability_stats['cv_coefficient']:.6f}")
        
        return stability_stats
    
    def generate_improvement_recommendations(self):
        """生成改进建议"""
        logger.info("生成改进建议")
        
        recommendations = []
        
        # 基于特征分析的建议
        if 'feature_types' in self.analysis_results:
            feature_types = self.analysis_results['feature_types']
            
            if feature_types['interaction'] < 10:
                recommendations.append("增加更多交互特征，当前交互特征较少")
            
            if feature_types['domain'] < 5:
                recommendations.append("增加更多领域知识特征")
            
            if feature_types['ext_source'] < 3:
                recommendations.append("充分利用EXT_SOURCE特征的组合")
        
        # 基于性能分析的建议
        if 'performance' in self.analysis_results:
            perf = self.analysis_results['performance']
            
            if perf['ensemble_improvement'] < 0.005:
                recommendations.append("集成效果不明显，需要增加模型多样性")
            
            if perf['diversity_score'] < 0.1:
                recommendations.append("模型相关性过高，需要增加多样性")
            
            if perf['avg_individual_auc'] < 0.79:
                recommendations.append("单模型性能有待提升，需要优化特征工程和超参数")
        
        # 基于数据质量的建议
        if 'data_quality' in self.analysis_results:
            dq = self.analysis_results['data_quality']
            
            if dq['high_missing_count'] > 20:
                recommendations.append("存在大量高缺失特征，需要改进缺失值处理")
            
            if dq['high_skew_count'] > 50:
                recommendations.append("存在大量偏斜特征，考虑对数变换或Box-Cox变换")
            
            if len(dq['constant_features']) > 0:
                recommendations.append("存在常数特征，应该移除")
            
            if len(dq['duplicate_features']) > 0:
                recommendations.append("存在重复特征，应该去重")
        
        # 基于CV稳定性的建议
        if 'cv_stability' in self.analysis_results:
            cv = self.analysis_results['cv_stability']
            
            if cv['cv_coefficient'] > 0.02:
                recommendations.append("交叉验证不稳定，可能存在数据泄露或过拟合")
            
            if cv['score_range'] > 0.01:
                recommendations.append("不同折之间性能差异较大，需要检查数据分布")
        
        self.analysis_results['recommendations'] = recommendations
        
        logger.info("改进建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"{i}. {rec}")
        
        return recommendations
    
    def save_analysis_report(self, output_path="performance_analysis_report.txt"):
        """保存分析报告"""
        logger.info(f"保存分析报告到 {output_path}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("Home Credit Default Risk - 性能分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 特征分析
            if 'feature_types' in self.analysis_results:
                f.write("1. 特征类型分布:\n")
                for ftype, count in self.analysis_results['feature_types'].items():
                    f.write(f"   {ftype}: {count}\n")
                f.write("\n")
            
            # 性能分析
            if 'performance' in self.analysis_results:
                perf = self.analysis_results['performance']
                f.write("2. 模型性能分析:\n")
                f.write(f"   平均单模型AUC: {perf['avg_individual_auc']:.6f}\n")
                f.write(f"   集成AUC: {perf['ensemble_auc']:.6f}\n")
                f.write(f"   集成提升: {perf['ensemble_improvement']:.6f}\n")
                f.write(f"   模型多样性分数: {perf['diversity_score']:.6f}\n\n")
            
            # 数据质量
            if 'data_quality' in self.analysis_results:
                dq = self.analysis_results['data_quality']
                f.write("3. 数据质量分析:\n")
                f.write(f"   总特征数: {dq['total_features']}\n")
                f.write(f"   高缺失特征数: {dq['high_missing_count']}\n")
                f.write(f"   高偏斜特征数: {dq['high_skew_count']}\n")
                f.write(f"   常数特征数: {len(dq['constant_features'])}\n")
                f.write(f"   重复特征对数: {len(dq['duplicate_features'])}\n\n")
            
            # 改进建议
            if 'recommendations' in self.analysis_results:
                f.write("4. 改进建议:\n")
                for i, rec in enumerate(self.analysis_results['recommendations'], 1):
                    f.write(f"   {i}. {rec}\n")
        
        logger.info("分析报告保存完成")
