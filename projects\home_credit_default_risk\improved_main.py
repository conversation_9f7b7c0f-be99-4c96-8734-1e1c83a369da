"""
Home Credit Default Risk - 改进版主程序

专注于关键改进技术的简化版本：
1. 增强特征工程
2. 优化的集成模型
3. 更好的数据清理

目标：将AUC从0.78提升到0.8+

作者：Augment Agent
"""

import pandas as pd
import numpy as np
import yaml
import logging
import os
import argparse
from datetime import datetime
from sklearn.metrics import roc_auc_score

from data_loader import DataLoader
from preprocessor import Preprocessor
from feature_engineering import FeatureEngineer
from advanced_feature_engineering import AdvancedFeatureEngineer
from ensemble_trainer import EnsembleTrainer


def setup_logging(log_dir="logs"):
    """设置日志记录"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"improved_pipeline_{timestamp}.log")

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_file), logging.StreamHandler()],
    )

    return logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Improved Home Credit Default Risk Pipeline"
    )
    parser.add_argument(
        "--config", type=str, default="config.yaml", help="配置文件路径"
    )
    parser.add_argument(
        "--output", type=str, default="improved_submission.csv", help="提交文件输出路径"
    )
    parser.add_argument("--log_dir", type=str, default="logs", help="日志目录")
    return parser.parse_args()


def clean_data_thoroughly(X, test_X, logger):
    """彻底清理数据"""
    logger.info("开始彻底数据清理")
    
    original_cols = X.columns.tolist()
    
    # 1. 处理非数值列
    for col in X.columns:
        if X[col].dtype == 'object':
            logger.warning(f"发现非数值列 {col}，尝试转换")
            try:
                X[col] = pd.to_numeric(X[col], errors='coerce')
                test_X[col] = pd.to_numeric(test_X[col], errors='coerce')
            except:
                logger.warning(f"删除无法转换的列: {col}")
                X = X.drop(columns=[col])
                test_X = test_X.drop(columns=[col])
                continue
    
    # 2. 处理无穷大值和NaN
    X = X.replace([np.inf, -np.inf], np.nan)
    test_X = test_X.replace([np.inf, -np.inf], np.nan)
    
    # 3. 处理极值
    for col in X.select_dtypes(include=[np.number]).columns:
        # 计算合理的上下界
        q01 = X[col].quantile(0.01)
        q99 = X[col].quantile(0.99)
        
        # 如果分位数是有效的，则进行裁剪
        if pd.notna(q01) and pd.notna(q99):
            X[col] = X[col].clip(lower=q01, upper=q99)
            test_X[col] = test_X[col].clip(lower=q01, upper=q99)
    
    # 4. 填充剩余的NaN值
    X = X.fillna(X.median())
    test_X = test_X.fillna(test_X.median())
    
    # 5. 最终检查
    logger.info(f"数据清理完成: {X.shape[1]}个特征")
    logger.info(f"数据类型: {X.dtypes.value_counts().to_dict()}")
    
    # 确保没有NaN或无穷大值
    assert not X.isnull().any().any(), "训练集仍有NaN值"
    assert not test_X.isnull().any().any(), "测试集仍有NaN值"
    assert not np.isinf(X.values).any(), "训练集仍有无穷大值"
    assert not np.isinf(test_X.values).any(), "测试集仍有无穷大值"
    
    return X, test_X


def main():
    """主函数"""
    args = parse_args()
    logger = setup_logging(args.log_dir)
    logger.info("开始执行Improved Home Credit Default Risk Pipeline")

    try:
        # 加载配置
        with open(args.config, "r", encoding="utf-8") as file:
            config = yaml.safe_load(file)

        # 1. 加载数据
        logger.info("步骤1: 加载数据")
        data_loader = DataLoader(config)
        data_dict = data_loader.load_data()
        train_df, test_df = data_loader.get_train_test_data()

        # 2. 数据预处理
        logger.info("步骤2: 数据预处理")
        preprocessor = Preprocessor()
        train_df_processed = preprocessor.preprocess(train_df, is_train=True)
        test_df_processed = preprocessor.preprocess(test_df, is_train=False)

        # 3. 基础特征工程
        logger.info("步骤3: 基础特征工程")
        feature_engineer = FeatureEngineer(data_dict)
        train_df_featured = feature_engineer.engineer_features(train_df_processed)
        test_df_featured = feature_engineer.engineer_features(test_df_processed)

        # 4. 高级特征工程（仅部分功能以提高稳定性）
        logger.info("步骤4: 高级特征工程")
        advanced_engineer = AdvancedFeatureEngineer(data_dict)
        
        # 只使用稳定的高级特征
        train_df_advanced = advanced_engineer.create_time_series_features(train_df_featured)
        test_df_advanced = advanced_engineer.create_time_series_features(test_df_featured)
        
        train_df_advanced = advanced_engineer.create_clustering_features(train_df_advanced)
        test_df_advanced = advanced_engineer.create_clustering_features(test_df_advanced)
        
        train_df_advanced = advanced_engineer.create_frequency_encoding_features(train_df_advanced)
        test_df_advanced = advanced_engineer.create_frequency_encoding_features(test_df_advanced)
        
        train_df_advanced = advanced_engineer.create_statistical_features(train_df_advanced)
        test_df_advanced = advanced_engineer.create_statistical_features(test_df_advanced)

        # 确保训练集和测试集有相同的列
        common_cols = [
            col for col in train_df_advanced.columns if col in test_df_advanced.columns
        ]
        common_cols = [col for col in common_cols if col not in ["TARGET", "SK_ID_CURR"]]

        logger.info(f"共有特征数量: {len(common_cols)}")

        # 准备训练数据
        X = train_df_advanced[common_cols]
        y = train_df_advanced["TARGET"]
        test_X = test_df_advanced[common_cols]
        test_ids = test_df_advanced["SK_ID_CURR"]

        # 清理特征名
        def clean_feature_names(df):
            df = df.copy()
            import re
            df.columns = [re.sub(r"[^a-zA-Z0-9_]", "_", col) for col in df.columns]
            return df

        X = clean_feature_names(X)
        test_X = clean_feature_names(test_X)

        # 彻底清理数据
        X, test_X = clean_data_thoroughly(X, test_X, logger)

        # 5. 改进的集成模型训练
        logger.info("步骤5: 改进的集成模型训练")
        ensemble_trainer = EnsembleTrainer(config)
        oof_preds, test_preds = ensemble_trainer.train_ensemble(X, y, test_X)

        # 6. 融合预测
        logger.info("步骤6: 融合预测")
        final_oof, final_test = ensemble_trainer.blend_predictions(oof_preds, test_preds, y)

        # 7. 生成提交文件
        logger.info("步骤7: 生成提交文件")
        submission = ensemble_trainer.generate_submission(final_test, test_ids, args.output)

        # 输出最终结果
        final_auc = roc_auc_score(y, final_oof)
        logger.info(f"最终交叉验证AUC: {final_auc:.6f}")
        
        if ensemble_trainer.feature_importances is not None:
            top_features = ensemble_trainer.feature_importances.head(20)
            logger.info("Top 20 重要特征:")
            for feature, importance in top_features.items():
                logger.info(f"  {feature}: {importance:.6f}")

        logger.info("Improved Pipeline执行完成")
        logger.info(f"提交文件已保存到: {args.output}")

    except Exception as e:
        logger.error(f"Pipeline执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    main()
