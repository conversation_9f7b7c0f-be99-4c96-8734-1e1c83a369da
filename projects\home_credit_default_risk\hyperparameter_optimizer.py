"""
超参数优化模块

使用Optuna进行贝叶斯优化，自动寻找最佳超参数组合。
支持XGBoost和LightGBM的超参数优化。

目标：通过精细的超参数调优提升模型性能

作者：Augment Agent
"""

import optuna
import numpy as np
import pandas as pd
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import roc_auc_score
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
import logging
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, X, y, n_trials=100, cv_folds=3, random_state=42):
        """
        初始化超参数优化器
        
        参数:
            X (DataFrame): 特征数据
            y (Series): 目标变量
            n_trials (int): 优化试验次数
            cv_folds (int): 交叉验证折数
            random_state (int): 随机种子
        """
        self.X = X
        self.y = y
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.best_params = {}
        
        # 设置交叉验证
        self.cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
    
    def objective_xgboost(self, trial):
        """XGBoost目标函数"""
        # 定义超参数搜索空间
        params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'random_state': self.random_state,
            'n_jobs': -1,
            'verbosity': 0,
            
            # 核心超参数
            'n_estimators': trial.suggest_int('n_estimators', 500, 3000, step=100),
            'max_depth': trial.suggest_int('max_depth', 3, 12),
            'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.1, log=True),
            'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
            
            # 正则化参数
            'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 1.0),
            'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 1.0),
            
            # 采样参数
            'subsample': trial.suggest_float('subsample', 0.6, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            'colsample_bylevel': trial.suggest_float('colsample_bylevel', 0.6, 1.0),
            
            # 其他参数
            'gamma': trial.suggest_float('gamma', 0.0, 1.0),
            'scale_pos_weight': trial.suggest_float('scale_pos_weight', 1.0, 10.0)
        }
        
        # 创建模型
        model = XGBClassifier(**params)
        
        # 交叉验证
        cv_scores = cross_val_score(
            model, self.X, self.y, 
            cv=self.cv, 
            scoring='roc_auc',
            n_jobs=1  # 避免嵌套并行
        )
        
        return cv_scores.mean()
    
    def objective_lightgbm(self, trial):
        """LightGBM目标函数"""
        # 定义超参数搜索空间
        params = {
            'objective': 'binary',
            'metric': 'auc',
            'boosting_type': 'gbdt',
            'random_state': self.random_state,
            'n_jobs': -1,
            'verbosity': -1,
            'force_col_wise': True,
            
            # 核心超参数
            'n_estimators': trial.suggest_int('n_estimators', 500, 3000, step=100),
            'num_leaves': trial.suggest_int('num_leaves', 10, 300),
            'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.1, log=True),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
            'min_child_weight': trial.suggest_float('min_child_weight', 1e-3, 10.0, log=True),
            
            # 正则化参数
            'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 1.0),
            'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 1.0),
            
            # 采样参数
            'bagging_fraction': trial.suggest_float('bagging_fraction', 0.6, 1.0),
            'feature_fraction': trial.suggest_float('feature_fraction', 0.6, 1.0),
            'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
            
            # 其他参数
            'max_depth': trial.suggest_int('max_depth', 3, 12),
            'min_split_gain': trial.suggest_float('min_split_gain', 0.0, 1.0),
            'scale_pos_weight': trial.suggest_float('scale_pos_weight', 1.0, 10.0)
        }
        
        # 创建模型
        model = LGBMClassifier(**params)
        
        # 交叉验证
        cv_scores = cross_val_score(
            model, self.X, self.y, 
            cv=self.cv, 
            scoring='roc_auc',
            n_jobs=1  # 避免嵌套并行
        )
        
        return cv_scores.mean()
    
    def optimize_xgboost(self):
        """优化XGBoost超参数"""
        logger.info(f"开始XGBoost超参数优化，试验次数: {self.n_trials}")
        
        # 创建研究对象
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=self.random_state)
        )
        
        # 执行优化
        study.optimize(self.objective_xgboost, n_trials=self.n_trials)
        
        # 保存最佳参数
        self.best_params['xgboost'] = study.best_params
        
        logger.info(f"XGBoost最佳AUC: {study.best_value:.6f}")
        logger.info(f"XGBoost最佳参数: {study.best_params}")
        
        return study.best_params, study.best_value
    
    def optimize_lightgbm(self):
        """优化LightGBM超参数"""
        logger.info(f"开始LightGBM超参数优化，试验次数: {self.n_trials}")
        
        # 创建研究对象
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=self.random_state)
        )
        
        # 执行优化
        study.optimize(self.objective_lightgbm, n_trials=self.n_trials)
        
        # 保存最佳参数
        self.best_params['lightgbm'] = study.best_params
        
        logger.info(f"LightGBM最佳AUC: {study.best_value:.6f}")
        logger.info(f"LightGBM最佳参数: {study.best_params}")
        
        return study.best_params, study.best_value
    
    def optimize_all_models(self):
        """优化所有模型的超参数"""
        logger.info("开始全模型超参数优化")
        
        results = {}
        
        # 优化XGBoost
        try:
            xgb_params, xgb_score = self.optimize_xgboost()
            results['xgboost'] = {'params': xgb_params, 'score': xgb_score}
        except Exception as e:
            logger.error(f"XGBoost优化失败: {e}")
            results['xgboost'] = None
        
        # 优化LightGBM
        try:
            lgb_params, lgb_score = self.optimize_lightgbm()
            results['lightgbm'] = {'params': lgb_params, 'score': lgb_score}
        except Exception as e:
            logger.error(f"LightGBM优化失败: {e}")
            results['lightgbm'] = None
        
        logger.info("全模型超参数优化完成")
        return results
    
    def get_optimized_model_configs(self):
        """获取优化后的模型配置"""
        configs = []
        
        # XGBoost配置
        if 'xgboost' in self.best_params:
            xgb_params = self.best_params['xgboost'].copy()
            xgb_params.update({
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'random_state': self.random_state,
                'n_jobs': -1,
                'verbosity': 0
            })
            
            configs.append({
                'name': 'xgb_optimized',
                'model': XGBClassifier,
                'params': xgb_params
            })
        
        # LightGBM配置
        if 'lightgbm' in self.best_params:
            lgb_params = self.best_params['lightgbm'].copy()
            lgb_params.update({
                'objective': 'binary',
                'metric': 'auc',
                'boosting_type': 'gbdt',
                'random_state': self.random_state,
                'n_jobs': -1,
                'verbosity': -1,
                'force_col_wise': True
            })
            
            configs.append({
                'name': 'lgb_optimized',
                'model': LGBMClassifier,
                'params': lgb_params
            })
        
        return configs
    
    def save_best_params(self, output_path="best_hyperparameters.json"):
        """保存最佳超参数到文件"""
        import json
        
        logger.info(f"保存最佳超参数到 {output_path}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.best_params, f, indent=2, ensure_ascii=False)
        
        logger.info("最佳超参数保存完成")


def quick_hyperparameter_search(X, y, model_type='both', n_trials=50):
    """
    快速超参数搜索函数
    
    参数:
        X (DataFrame): 特征数据
        y (Series): 目标变量
        model_type (str): 模型类型 ('xgboost', 'lightgbm', 'both')
        n_trials (int): 试验次数
    
    返回:
        dict: 优化结果
    """
    optimizer = HyperparameterOptimizer(X, y, n_trials=n_trials)
    
    if model_type == 'xgboost':
        return {'xgboost': optimizer.optimize_xgboost()}
    elif model_type == 'lightgbm':
        return {'lightgbm': optimizer.optimize_lightgbm()}
    else:
        return optimizer.optimize_all_models()
