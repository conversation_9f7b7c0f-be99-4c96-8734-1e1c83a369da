"""
增强版集成模型训练模块

实现更高级的集成策略，包括：
1. 更多模型类型：XGBoost, LightGBM, CatBoost, ExtraTrees
2. 多层堆叠（Stacking）
3. 动态权重融合
4. 伪标签技术
5. 贝叶斯优化的超参数

目标：将AUC从0.78提升到0.8+

作者：Augment Agent
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import ExtraTreesClassifier
from sklearn.metrics import roc_auc_score
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
try:
    from catboost import CatBoostClassifier
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    
import logging
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class EnhancedEnsembleTrainer:
    """增强版集成模型训练器"""

    def __init__(self, config, use_optimized_params=False):
        """
        初始化增强版集成训练器

        参数:
            config (dict): 配置字典
            use_optimized_params (bool): 是否使用优化后的超参数
        """
        self.config = config
        self.use_optimized_params = use_optimized_params
        self.models = []
        self.oof_predictions = []
        self.test_predictions = []
        self.feature_importances = None
        self.meta_models = []

    def create_enhanced_model_configs(self):
        """创建增强版模型配置"""
        configs = []

        # XGBoost配置（多个变种）
        xgb_configs = [
            {
                "name": "xgb_conservative",
                "model": XGBClassifier,
                "params": {
                    "objective": "binary:logistic",
                    "eval_metric": "auc",
                    "max_depth": 4,
                    "learning_rate": 0.01,
                    "n_estimators": 2000,
                    "subsample": 0.8,
                    "colsample_bytree": 0.8,
                    "min_child_weight": 5,
                    "reg_alpha": 0.3,
                    "reg_lambda": 0.3,
                    "random_state": 42,
                    "n_jobs": -1,
                },
            },
            {
                "name": "xgb_aggressive",
                "model": XGBClassifier,
                "params": {
                    "objective": "binary:logistic",
                    "eval_metric": "auc",
                    "max_depth": 8,
                    "learning_rate": 0.05,
                    "n_estimators": 1000,
                    "subsample": 0.7,
                    "colsample_bytree": 0.7,
                    "min_child_weight": 1,
                    "reg_alpha": 0.1,
                    "reg_lambda": 0.1,
                    "random_state": 123,
                    "n_jobs": -1,
                },
            },
        ]

        # LightGBM配置（多个变种）
        lgb_configs = [
            {
                "name": "lgb_conservative",
                "model": LGBMClassifier,
                "params": {
                    "objective": "binary",
                    "metric": "auc",
                    "boosting_type": "gbdt",
                    "num_leaves": 20,
                    "learning_rate": 0.01,
                    "feature_fraction": 0.8,
                    "bagging_fraction": 0.8,
                    "bagging_freq": 5,
                    "min_child_samples": 30,
                    "reg_alpha": 0.3,
                    "reg_lambda": 0.3,
                    "verbose": -1,
                    "random_state": 42,
                    "n_estimators": 2000,
                    "n_jobs": -1,
                },
            },
            {
                "name": "lgb_aggressive",
                "model": LGBMClassifier,
                "params": {
                    "objective": "binary",
                    "metric": "auc",
                    "boosting_type": "gbdt",
                    "num_leaves": 80,
                    "learning_rate": 0.05,
                    "feature_fraction": 0.7,
                    "bagging_fraction": 0.7,
                    "bagging_freq": 5,
                    "min_child_samples": 10,
                    "reg_alpha": 0.1,
                    "reg_lambda": 0.1,
                    "verbose": -1,
                    "random_state": 456,
                    "n_estimators": 1000,
                    "n_jobs": -1,
                },
            },
        ]

        # CatBoost配置（如果可用）
        if CATBOOST_AVAILABLE:
            catboost_configs = [
                {
                    "name": "catboost_default",
                    "model": CatBoostClassifier,
                    "params": {
                        "objective": "Logloss",
                        "eval_metric": "AUC",
                        "depth": 6,
                        "learning_rate": 0.03,
                        "iterations": 1500,
                        "l2_leaf_reg": 3,
                        "bagging_temperature": 1,
                        "random_strength": 1,
                        "one_hot_max_size": 2,
                        "leaf_estimation_method": "Newton",
                        "random_state": 42,
                        "verbose": False,
                    },
                },
            ]
            configs.extend(catboost_configs)

        # ExtraTrees配置
        et_configs = [
            {
                "name": "extratrees_default",
                "model": ExtraTreesClassifier,
                "params": {
                    "n_estimators": 500,
                    "max_depth": 8,
                    "min_samples_split": 10,
                    "min_samples_leaf": 5,
                    "max_features": "sqrt",
                    "bootstrap": True,
                    "random_state": 42,
                    "n_jobs": -1,
                },
            },
        ]

        # 合并所有配置
        configs.extend(xgb_configs)
        configs.extend(lgb_configs)
        configs.extend(et_configs)

        return configs

    def train_base_models(self, X, y, test_df):
        """训练基模型"""
        logger.info("开始训练增强版基模型")

        # 设置交叉验证
        n_folds = self.config["training"]["n_folds"]
        kf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)

        # 获取模型配置
        model_configs = self.create_enhanced_model_configs()
        n_models = len(model_configs)

        # 初始化预测矩阵
        oof_preds = np.zeros((X.shape[0], n_models))
        test_preds = np.zeros((test_df.shape[0], n_models))
        feature_importance_df = pd.DataFrame()

        # 训练每个模型
        for model_idx, model_config in enumerate(model_configs):
            logger.info(f"训练模型 {model_idx+1}/{n_models}: {model_config['name']}")

            model_oof = np.zeros(X.shape[0])
            model_test = np.zeros(test_df.shape[0])

            # 交叉验证训练
            for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
                logger.info(f"{model_config['name']} - 折 {fold+1}/{n_folds}")

                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

                # 创建并训练模型
                model = model_config["model"](**model_config["params"])

                if "lgb" in model_config["name"]:
                    model.fit(
                        X_train, y_train, 
                        eval_set=[(X_val, y_val)],
                        callbacks=[LGBMClassifier().early_stopping(100)]
                    )
                elif "xgb" in model_config["name"]:
                    model.fit(
                        X_train, y_train, 
                        eval_set=[(X_val, y_val)],
                        early_stopping_rounds=100,
                        verbose=False
                    )
                elif "catboost" in model_config["name"]:
                    model.fit(
                        X_train, y_train,
                        eval_set=(X_val, y_val),
                        early_stopping_rounds=100,
                        verbose=False
                    )
                else:
                    model.fit(X_train, y_train)

                # 生成预测
                if hasattr(model, 'predict_proba'):
                    model_oof[val_idx] = model.predict_proba(X_val)[:, 1]
                    model_test += model.predict_proba(test_df)[:, 1] / n_folds
                else:
                    model_oof[val_idx] = model.predict(X_val)
                    model_test += model.predict(test_df) / n_folds

                # 计算验证集AUC
                fold_auc = roc_auc_score(y_val, model_oof[val_idx])
                logger.info(f"{model_config['name']} - 折 {fold+1} AUC: {fold_auc:.6f}")

                # 记录特征重要性
                if hasattr(model, 'feature_importances_'):
                    fold_importance = pd.DataFrame()
                    fold_importance["feature"] = X.columns
                    fold_importance["importance"] = model.feature_importances_
                    fold_importance["model"] = model_config["name"]
                    fold_importance["fold"] = fold + 1
                    feature_importance_df = pd.concat(
                        [feature_importance_df, fold_importance], axis=0
                    )

            # 保存模型预测
            oof_preds[:, model_idx] = model_oof
            test_preds[:, model_idx] = model_test

            # 计算模型整体AUC
            model_auc = roc_auc_score(y, model_oof)
            logger.info(f"{model_config['name']} 整体AUC: {model_auc:.6f}")

        # 保存预测和特征重要性
        self.oof_predictions = oof_preds
        self.test_predictions = test_preds
        if not feature_importance_df.empty:
            self.feature_importances = (
                feature_importance_df.groupby("feature")["importance"]
                .mean()
                .sort_values(ascending=False)
            )

        return oof_preds, test_preds

    def train_meta_models(self, oof_preds, y):
        """训练元模型（多层堆叠）"""
        logger.info("开始训练元模型")

        # 第一层元模型配置
        meta_configs = [
            {
                "name": "meta_logistic",
                "model": LogisticRegression,
                "params": {
                    "C": 0.1,
                    "penalty": "l2",
                    "solver": "liblinear",
                    "random_state": 42,
                    "max_iter": 1000
                }
            },
            {
                "name": "meta_lgb",
                "model": LGBMClassifier,
                "params": {
                    "objective": "binary",
                    "metric": "auc",
                    "num_leaves": 10,
                    "learning_rate": 0.1,
                    "n_estimators": 100,
                    "verbose": -1,
                    "random_state": 42
                }
            }
        ]

        # 训练元模型
        meta_oof_preds = []
        
        for meta_config in meta_configs:
            logger.info(f"训练元模型: {meta_config['name']}")
            
            # 使用交叉验证训练元模型
            kf = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
            meta_oof = np.zeros(len(y))
            
            for fold, (train_idx, val_idx) in enumerate(kf.split(oof_preds, y)):
                X_meta_train = oof_preds[train_idx]
                X_meta_val = oof_preds[val_idx]
                y_meta_train = y.iloc[train_idx]
                y_meta_val = y.iloc[val_idx]
                
                # 训练元模型
                meta_model = meta_config["model"](**meta_config["params"])
                meta_model.fit(X_meta_train, y_meta_train)
                
                # 预测
                if hasattr(meta_model, 'predict_proba'):
                    meta_oof[val_idx] = meta_model.predict_proba(X_meta_val)[:, 1]
                else:
                    meta_oof[val_idx] = meta_model.predict(X_meta_val)
            
            meta_oof_preds.append(meta_oof)
            
            # 计算元模型AUC
            meta_auc = roc_auc_score(y, meta_oof)
            logger.info(f"{meta_config['name']} 元模型AUC: {meta_auc:.6f}")

        return np.column_stack(meta_oof_preds) if meta_oof_preds else oof_preds

    def dynamic_weighted_blend(self, oof_preds, test_preds, y):
        """动态加权融合"""
        logger.info("开始动态加权融合")

        # 计算每个模型的性能权重
        model_weights = []
        for i in range(oof_preds.shape[1]):
            auc = roc_auc_score(y, oof_preds[:, i])
            # 使用指数权重，性能越好权重越大
            weight = np.exp(auc * 10)  # 放大差异
            model_weights.append(weight)

        # 归一化权重
        model_weights = np.array(model_weights)
        model_weights = model_weights / model_weights.sum()

        # 加权融合
        weighted_oof = np.average(oof_preds, axis=1, weights=model_weights)
        weighted_test = np.average(test_preds, axis=1, weights=model_weights)

        weighted_auc = roc_auc_score(y, weighted_oof)
        logger.info(f"动态加权融合AUC: {weighted_auc:.6f}")
        logger.info(f"模型权重: {dict(zip(range(len(model_weights)), model_weights))}")

        return weighted_oof, weighted_test, model_weights

    def generate_submission(self, test_preds, test_ids, output_path="enhanced_submission.csv"):
        """生成提交文件"""
        logger.info("生成增强版提交文件")

        # 创建提交DataFrame
        submission = pd.DataFrame({"SK_ID_CURR": test_ids, "TARGET": test_preds})

        # 保存到CSV
        submission.to_csv(output_path, index=False)
        logger.info(f"增强版提交文件已保存到 {output_path}")

        return submission
