"""
Home Credit Default Risk - 改进版终极主程序

基于0.788的成功经验，采用更平衡的方法：
1. 适度的特征工程（700-800个特征）
2. 保守的特征选择
3. 简化的模型集成
4. 避免伪标签技术
5. 重点关注特征质量而非数量

目标：稳定达到AUC 0.79+

作者：Augment Agent
"""

import pandas as pd
import numpy as np
import yaml
import logging
import os
from datetime import datetime
from sklearn.metrics import roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif

from data_loader import DataLoader
from preprocessor import Preprocessor
from feature_engineering import FeatureEngineer
from credit_specific_features import CreditSpecificFeatureEngineer
from multi_model_ensemble import MultiModelEnsemble


def setup_logging():
    """设置日志记录"""
    if not os.path.exists("logs"):
        os.makedirs("logs")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"logs/improved_ultimate_pipeline_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'), 
            logging.StreamHandler()
        ],
    )

    return logging.getLogger(__name__)


def conservative_feature_selection(X, y, n_features=600):
    """保守的特征选择策略"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始保守特征选择，从{X.shape[1]}个特征中选择{n_features}个")
    
    # 确保没有NaN值
    X_clean = X.fillna(0)
    
    # 使用F统计量进行特征选择
    try:
        selector = SelectKBest(score_func=f_classif, k=min(n_features, X.shape[1]))
        X_selected = selector.fit_transform(X_clean, y)
        selected_features = X.columns[selector.get_support()].tolist()
        
        logger.info(f"特征选择完成，选择了{len(selected_features)}个特征")
        return X[selected_features], selected_features
        
    except Exception as e:
        logger.warning(f"特征选择失败: {e}，使用前{n_features}个特征")
        selected_features = X.columns[:n_features].tolist()
        return X[selected_features], selected_features


def clean_and_prepare_data(X, test_X, logger):
    """改进的数据清理和准备"""
    logger.info("开始改进的数据清理")
    
    # 处理分类列
    for col in X.columns:
        if X[col].dtype == 'object' or str(X[col].dtype).startswith('category'):
            logger.info(f"处理分类列: {col}")
            from sklearn.preprocessing import LabelEncoder
            le = LabelEncoder()
            
            combined = pd.concat([X[col], test_X[col]], axis=0)
            combined_encoded = le.fit_transform(combined.astype(str))
            
            X[col] = combined_encoded[:len(X)]
            test_X[col] = combined_encoded[len(X):]
    
    # 处理无穷大值
    X = X.replace([np.inf, -np.inf], np.nan)
    test_X = test_X.replace([np.inf, -np.inf], np.nan)
    
    # 处理极值（更保守的方法）
    for col in X.select_dtypes(include=[np.number]).columns:
        q01 = X[col].quantile(0.01)  # 更保守的极值处理
        q99 = X[col].quantile(0.99)
        
        if pd.notna(q01) and pd.notna(q99) and q01 != q99:
            X[col] = X[col].clip(lower=q01, upper=q99)
            test_X[col] = test_X[col].clip(lower=q01, upper=q99)
    
    # 智能填充缺失值
    logger.info("开始智能填充缺失值")
    for col in X.columns:
        if X[col].isnull().sum() > 0:
            if X[col].dtype in ['int64', 'float64']:
                # 对于数值列，使用中位数填充
                fill_value = X[col].median()
                if pd.isna(fill_value):
                    fill_value = 0
                X[col] = X[col].fillna(fill_value)
                test_X[col] = test_X[col].fillna(fill_value)
            else:
                # 对于分类列，使用众数
                mode_values = X[col].mode()
                if len(mode_values) > 0 and pd.notna(mode_values.iloc[0]):
                    fill_value = mode_values.iloc[0]
                else:
                    fill_value = 0
                X[col] = X[col].fillna(fill_value)
                test_X[col] = test_X[col].fillna(fill_value)
    
    # 最终检查
    logger.info(f"数据清理完成: {X.shape}")
    
    # 确保没有问题
    assert not X.isnull().any().any(), "训练集仍有NaN值"
    assert not test_X.isnull().any().any(), "测试集仍有NaN值"
    
    return X, test_X


def create_simplified_ensemble(X, y, test_X):
    """创建简化的模型集成"""
    logger = logging.getLogger(__name__)
    logger.info("开始简化模型集成训练")
    
    from sklearn.model_selection import StratifiedKFold
    from sklearn.metrics import roc_auc_score
    from lightgbm import LGBMClassifier
    from xgboost import XGBClassifier
    from sklearn.ensemble import ExtraTreesClassifier
    
    # 简化的模型配置（只使用最稳定的模型）
    models = [
        {
            'name': 'lgb_conservative',
            'model': LGBMClassifier(
                objective='binary',
                metric='auc',
                num_leaves=31,
                learning_rate=0.02,
                n_estimators=2000,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                verbose=-1,
                random_state=42
            )
        },
        {
            'name': 'xgb_conservative',
            'model': XGBClassifier(
                objective='binary:logistic',
                eval_metric='auc',
                max_depth=6,
                learning_rate=0.02,
                n_estimators=2000,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                n_jobs=-1
            )
        },
        {
            'name': 'et_conservative',
            'model': ExtraTreesClassifier(
                n_estimators=500,
                max_depth=10,
                min_samples_split=10,
                min_samples_leaf=5,
                random_state=42,
                n_jobs=-1
            )
        }
    ]
    
    # 交叉验证设置
    kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # 存储预测结果
    oof_predictions = np.zeros((X.shape[0], len(models)))
    test_predictions = np.zeros((test_X.shape[0], len(models)))
    
    # 训练每个模型
    for model_idx, model_config in enumerate(models):
        logger.info(f"训练模型 {model_idx+1}/{len(models)}: {model_config['name']}")
        
        model_oof = np.zeros(X.shape[0])
        model_test = np.zeros(test_X.shape[0])
        
        for fold, (train_idx, val_idx) in enumerate(kf.split(X, y)):
            logger.info(f"{model_config['name']} - 折 {fold+1}/5")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # 训练模型
            model = model_config['model']
            model.fit(X_train, y_train)
            
            # 预测
            if hasattr(model, 'predict_proba'):
                model_oof[val_idx] = model.predict_proba(X_val)[:, 1]
                model_test += model.predict_proba(test_X)[:, 1] / 5
            else:
                model_oof[val_idx] = model.predict(X_val)
                model_test += model.predict(test_X) / 5
            
            # 计算验证集AUC
            fold_auc = roc_auc_score(y_val, model_oof[val_idx])
            logger.info(f"{model_config['name']} - 折 {fold+1} AUC: {fold_auc:.6f}")
        
        # 保存预测
        oof_predictions[:, model_idx] = model_oof
        test_predictions[:, model_idx] = model_test
        
        # 计算模型整体AUC
        model_auc = roc_auc_score(y, model_oof)
        logger.info(f"{model_config['name']} 整体AUC: {model_auc:.6f}")
    
    # 简单平均融合
    final_oof = np.mean(oof_predictions, axis=1)
    final_test = np.mean(test_predictions, axis=1)
    
    final_auc = roc_auc_score(y, final_oof)
    logger.info(f"集成模型最终AUC: {final_auc:.6f}")
    
    return final_oof, final_test


def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始执行改进版终极Pipeline")
    logger.info("目标：稳定达到AUC 0.79+")

    try:
        # 加载配置
        with open("config.yaml", "r", encoding="utf-8") as file:
            config = yaml.safe_load(file)

        # 1. 加载数据
        logger.info("步骤1: 加载数据")
        data_loader = DataLoader(config)
        data_dict = data_loader.load_data()
        train_df, test_df = data_loader.get_train_test_data()

        # 2. 数据预处理
        logger.info("步骤2: 数据预处理")
        preprocessor = Preprocessor()
        train_df_processed = preprocessor.preprocess(train_df, is_train=True)
        test_df_processed = preprocessor.preprocess(test_df, is_train=False)

        # 3. 基础特征工程
        logger.info("步骤3: 基础特征工程")
        feature_engineer = FeatureEngineer(data_dict)
        train_df_featured = feature_engineer.engineer_features(train_df_processed)
        test_df_featured = feature_engineer.engineer_features(test_df_processed)

        # 4. Home Credit专用特征工程（适度）
        logger.info("步骤4: 适度的Home Credit专用特征工程")
        credit_engineer = CreditSpecificFeatureEngineer(data_dict)
        train_df_enhanced = credit_engineer.engineer_all_features(train_df_featured)
        test_df_enhanced = credit_engineer.engineer_all_features(test_df_featured)

        # 5. 准备数据
        logger.info("步骤5: 准备数据")
        common_cols = [
            col for col in train_df_enhanced.columns if col in test_df_enhanced.columns
        ]
        common_cols = [col for col in common_cols if col not in ["TARGET", "SK_ID_CURR"]]

        logger.info(f"总特征数量: {len(common_cols)}")

        X = train_df_enhanced[common_cols]
        y = train_df_enhanced["TARGET"]
        test_X = test_df_enhanced[common_cols]
        test_ids = test_df_enhanced["SK_ID_CURR"]

        # 清理特征名
        def clean_feature_names(df):
            df = df.copy()
            import re
            df.columns = [re.sub(r"[^a-zA-Z0-9_]", "_", col) for col in df.columns]
            return df

        X = clean_feature_names(X)
        test_X = clean_feature_names(test_X)

        # 6. 数据清理
        X, test_X = clean_and_prepare_data(X, test_X, logger)

        # 7. 保守的特征选择
        logger.info("步骤6: 保守的特征选择")
        X_selected, selected_features = conservative_feature_selection(X, y, n_features=600)
        test_X_selected = test_X[selected_features]

        # 8. 简化的模型集成
        logger.info("步骤7: 简化的模型集成")
        final_oof, final_test = create_simplified_ensemble(X_selected, y, test_X_selected)

        # 9. 生成提交文件
        logger.info("步骤8: 生成提交文件")
        submission = pd.DataFrame({
            "SK_ID_CURR": test_ids,
            "TARGET": final_test
        })
        submission.to_csv("improved_ultimate_submission.csv", index=False)

        # 输出最终结果
        final_auc = roc_auc_score(y, final_oof)
        logger.info(f"最终交叉验证AUC: {final_auc:.6f}")
        logger.info("改进版终极Pipeline执行完成")
        logger.info(f"提交文件已保存到: improved_ultimate_submission.csv")
        logger.info(f"预测均值: {final_test.mean():.6f}")
        logger.info(f"预测标准差: {final_test.std():.6f}")

    except Exception as e:
        logger.error(f"Pipeline执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    main()
