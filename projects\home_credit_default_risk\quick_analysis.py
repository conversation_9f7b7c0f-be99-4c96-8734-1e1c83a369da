"""
快速分析脚本

分析为什么终极版本得分0.74
"""

import pandas as pd
import numpy as np
import logging

def setup_logging():
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def analyze_submissions():
    """分析所有提交文件"""
    logger = setup_logging()
    
    submissions = {}
    
    # 尝试加载所有提交文件
    files_to_check = [
        ("ensemble", "ensemble_submission.csv"),
        ("optimized", "optimized_submission.csv"), 
        ("ultimate", "ultimate_submission.csv")
    ]
    
    for name, filename in files_to_check:
        try:
            df = pd.read_csv(filename)
            submissions[name] = df
            logger.info(f"成功加载 {filename}: {df.shape}")
        except FileNotFoundError:
            logger.warning(f"找不到文件: {filename}")
    
    # 分析每个提交文件
    for name, df in submissions.items():
        logger.info(f"\n=== {name.upper()} 提交文件分析 ===")
        predictions = df['TARGET']
        
        logger.info(f"预测值统计:")
        logger.info(f"  数量: {len(predictions)}")
        logger.info(f"  最小值: {predictions.min():.6f}")
        logger.info(f"  最大值: {predictions.max():.6f}")
        logger.info(f"  平均值: {predictions.mean():.6f}")
        logger.info(f"  中位数: {predictions.median():.6f}")
        logger.info(f"  标准差: {predictions.std():.6f}")
        
        # 检查异常值
        if predictions.min() < 0:
            logger.warning(f"  发现负值: {(predictions < 0).sum()}个")
        if predictions.max() > 1:
            logger.warning(f"  发现大于1的值: {(predictions > 1).sum()}个")
        
        # 检查极端值
        extreme_low = (predictions < 0.01).sum()
        extreme_high = (predictions > 0.99).sum()
        logger.info(f"  极低值(<0.01): {extreme_low}个 ({extreme_low/len(predictions)*100:.2f}%)")
        logger.info(f"  极高值(>0.99): {extreme_high}个 ({extreme_high/len(predictions)*100:.2f}%)")
        
        # 分布分析
        logger.info(f"  预测分布:")
        for i in range(10):
            lower = i * 0.1
            upper = (i + 1) * 0.1
            count = ((predictions >= lower) & (predictions < upper)).sum()
            if i == 9:  # 最后一个区间包含1.0
                count = ((predictions >= lower) & (predictions <= upper)).sum()
            logger.info(f"    [{lower:.1f}-{upper:.1f}]: {count}个 ({count/len(predictions)*100:.2f}%)")
    
    # 对比分析
    if len(submissions) > 1:
        logger.info(f"\n=== 提交文件对比 ===")
        
        # 计算相关性
        if 'optimized' in submissions and 'ultimate' in submissions:
            corr = submissions['optimized']['TARGET'].corr(submissions['ultimate']['TARGET'])
            logger.info(f"优化版本 vs 终极版本相关性: {corr:.6f}")
            
            # 差异分析
            diff = submissions['ultimate']['TARGET'] - submissions['optimized']['TARGET']
            logger.info(f"预测差异 (终极 - 优化):")
            logger.info(f"  平均差异: {diff.mean():.6f}")
            logger.info(f"  差异标准差: {diff.std():.6f}")
            logger.info(f"  最大正差异: {diff.max():.6f}")
            logger.info(f"  最大负差异: {diff.min():.6f}")
            
            # 分析差异分布
            logger.info(f"  差异分布:")
            logger.info(f"    差异>0.1: {(diff > 0.1).sum()}个")
            logger.info(f"    差异>0.05: {(diff > 0.05).sum()}个")
            logger.info(f"    |差异|<0.01: {(np.abs(diff) < 0.01).sum()}个")
            logger.info(f"    差异<-0.05: {(diff < -0.05).sum()}个")
            logger.info(f"    差异<-0.1: {(diff < -0.1).sum()}个")

def diagnose_issues():
    """诊断可能的问题"""
    logger = setup_logging()
    
    logger.info(f"\n=== 问题诊断 ===")
    
    # 已知问题
    issues = [
        "1. 特征数量过多 (1580个) 可能导致过拟合",
        "2. 特征选择从1580个选800个可能丢失重要信息",
        "3. 高阶交互特征 (687个) 可能引入噪声",
        "4. 时间窗口特征的大量缺失值填充可能有偏差",
        "5. 伪标签技术可能引入错误标签",
        "6. 多模型集成权重可能不合理",
        "7. 数据预处理过程可能有问题"
    ]
    
    for issue in issues:
        logger.warning(issue)
    
    # 建议的解决方案
    logger.info(f"\n=== 解决方案建议 ===")
    solutions = [
        "1. 回退到更简单的特征工程 (500-700个特征)",
        "2. 使用更保守的特征选择策略",
        "3. 减少高阶交互特征的数量",
        "4. 改进缺失值处理策略",
        "5. 避免使用伪标签技术",
        "6. 简化模型集成，使用3-5个稳定模型",
        "7. 重新检查数据预处理流程"
    ]
    
    for solution in solutions:
        logger.info(solution)

def create_corrected_submission():
    """创建修正版本的提交文件"""
    logger = setup_logging()
    
    try:
        # 加载优化版本作为基准
        optimized = pd.read_csv("optimized_submission.csv")
        ultimate = pd.read_csv("ultimate_submission.csv")
        
        logger.info("创建修正版本提交文件")
        
        # 方法1: 简单平均
        avg_predictions = (optimized['TARGET'] + ultimate['TARGET']) / 2
        
        # 方法2: 加权平均 (给优化版本更高权重，因为它表现更好)
        weighted_predictions = 0.7 * optimized['TARGET'] + 0.3 * ultimate['TARGET']
        
        # 方法3: 使用优化版本，但在极端情况下使用终极版本
        corrected_predictions = optimized['TARGET'].copy()
        
        # 创建修正版本
        corrected_submission = pd.DataFrame({
            "SK_ID_CURR": optimized["SK_ID_CURR"],
            "TARGET": weighted_predictions  # 使用加权平均
        })
        
        corrected_submission.to_csv("corrected_submission.csv", index=False)
        
        logger.info("修正版本提交文件已生成: corrected_submission.csv")
        logger.info(f"修正版本预测均值: {weighted_predictions.mean():.6f}")
        logger.info(f"修正版本预测标准差: {weighted_predictions.std():.6f}")
        
        return corrected_submission
        
    except Exception as e:
        logger.error(f"创建修正版本失败: {e}")
        return None

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始快速分析")
    
    # 1. 分析提交文件
    analyze_submissions()
    
    # 2. 诊断问题
    diagnose_issues()
    
    # 3. 创建修正版本
    corrected_sub = create_corrected_submission()
    
    logger.info("\n=== 总结 ===")
    logger.info("终极版本得分0.74的主要原因可能是:")
    logger.info("1. 过度复杂的特征工程导致过拟合")
    logger.info("2. 特征选择策略不当")
    logger.info("3. 模型集成权重不合理")
    logger.info("\n推荐使用:")
    logger.info("- optimized_submission.csv (AUC 0.789)")
    logger.info("- 或 corrected_submission.csv (加权融合版本)")

if __name__ == "__main__":
    main()
