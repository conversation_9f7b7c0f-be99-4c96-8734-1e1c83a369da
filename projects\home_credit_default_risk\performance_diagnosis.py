"""
性能诊断工具

分析为什么终极版本得分0.74，比预期的0.8+低很多
可能的原因：
1. 特征选择过度
2. 数据泄露
3. 过拟合
4. 特征质量问题
5. 模型配置问题

作者：Augment Agent
"""

import pandas as pd
import numpy as np
import yaml
import logging
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import cross_val_score, StratifiedKFold
from lightgbm import LGBMClassifier
import warnings
warnings.filterwarnings('ignore')

def setup_logging():
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger(__name__)

def load_data():
    """加载数据"""
    logger = logging.getLogger(__name__)

    # 加载配置
    with open("config.yaml", "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)

    # 加载原始数据
    train_path = config["datasets"]["application_train"]
    test_path = config["datasets"]["application_test"]

    train_df = pd.read_csv(train_path)
    test_df = pd.read_csv(test_path)

    logger.info(f"原始训练集形状: {train_df.shape}")
    logger.info(f"原始测试集形状: {test_df.shape}")

    return train_df, test_df

def test_baseline_performance(train_df):
    """测试基线性能"""
    logger = logging.getLogger(__name__)
    logger.info("测试基线性能（仅使用原始特征）")
    
    # 使用原始特征
    feature_cols = [col for col in train_df.columns if col not in ['TARGET', 'SK_ID_CURR']]
    X = train_df[feature_cols]
    y = train_df['TARGET']
    
    # 简单预处理
    for col in X.columns:
        if X[col].dtype == 'object':
            X[col] = pd.Categorical(X[col]).codes
    
    X = X.fillna(X.median())
    
    # 快速模型测试
    model = LGBMClassifier(
        objective='binary',
        metric='auc',
        num_leaves=31,
        learning_rate=0.1,
        n_estimators=100,
        verbose=-1,
        random_state=42
    )
    
    # 交叉验证
    cv_scores = cross_val_score(
        model, X, y, 
        cv=StratifiedKFold(n_splits=3, shuffle=True, random_state=42),
        scoring='roc_auc'
    )
    
    baseline_auc = cv_scores.mean()
    logger.info(f"基线AUC (原始特征): {baseline_auc:.6f} ± {cv_scores.std():.6f}")
    
    return baseline_auc

def analyze_submission_file():
    """分析提交文件"""
    logger = logging.getLogger(__name__)
    
    try:
        submission = pd.read_csv("ultimate_submission.csv")
        logger.info(f"提交文件形状: {submission.shape}")
        logger.info(f"提交文件列名: {submission.columns.tolist()}")
        
        # 分析预测分布
        predictions = submission['TARGET']
        logger.info(f"预测值统计:")
        logger.info(f"  最小值: {predictions.min():.6f}")
        logger.info(f"  最大值: {predictions.max():.6f}")
        logger.info(f"  平均值: {predictions.mean():.6f}")
        logger.info(f"  中位数: {predictions.median():.6f}")
        logger.info(f"  标准差: {predictions.std():.6f}")
        
        # 检查异常值
        if predictions.min() < 0 or predictions.max() > 1:
            logger.warning("预测值超出[0,1]范围！")
        
        # 检查预测分布
        logger.info(f"预测值分布:")
        logger.info(f"  [0.0-0.1]: {((predictions >= 0.0) & (predictions < 0.1)).sum()}")
        logger.info(f"  [0.1-0.2]: {((predictions >= 0.1) & (predictions < 0.2)).sum()}")
        logger.info(f"  [0.2-0.3]: {((predictions >= 0.2) & (predictions < 0.3)).sum()}")
        logger.info(f"  [0.3-0.4]: {((predictions >= 0.3) & (predictions < 0.4)).sum()}")
        logger.info(f"  [0.4-0.5]: {((predictions >= 0.4) & (predictions < 0.5)).sum()}")
        logger.info(f"  [0.5-0.6]: {((predictions >= 0.5) & (predictions < 0.6)).sum()}")
        logger.info(f"  [0.6-0.7]: {((predictions >= 0.6) & (predictions < 0.7)).sum()}")
        logger.info(f"  [0.7-0.8]: {((predictions >= 0.7) & (predictions < 0.8)).sum()}")
        logger.info(f"  [0.8-0.9]: {((predictions >= 0.8) & (predictions < 0.9)).sum()}")
        logger.info(f"  [0.9-1.0]: {((predictions >= 0.9) & (predictions <= 1.0)).sum()}")
        
        return submission
        
    except FileNotFoundError:
        logger.error("找不到ultimate_submission.csv文件")
        return None

def compare_with_optimized_submission():
    """与优化版本提交文件对比"""
    logger = logging.getLogger(__name__)
    
    try:
        ultimate_sub = pd.read_csv("ultimate_submission.csv")
        optimized_sub = pd.read_csv("optimized_submission.csv")
        
        logger.info("对比终极版本与优化版本:")
        logger.info(f"终极版本预测均值: {ultimate_sub['TARGET'].mean():.6f}")
        logger.info(f"优化版本预测均值: {optimized_sub['TARGET'].mean():.6f}")
        logger.info(f"终极版本预测标准差: {ultimate_sub['TARGET'].std():.6f}")
        logger.info(f"优化版本预测标准差: {optimized_sub['TARGET'].std():.6f}")
        
        # 计算相关性
        correlation = ultimate_sub['TARGET'].corr(optimized_sub['TARGET'])
        logger.info(f"两个预测的相关性: {correlation:.6f}")
        
        # 分析差异
        diff = ultimate_sub['TARGET'] - optimized_sub['TARGET']
        logger.info(f"预测差异统计:")
        logger.info(f"  平均差异: {diff.mean():.6f}")
        logger.info(f"  差异标准差: {diff.std():.6f}")
        logger.info(f"  最大正差异: {diff.max():.6f}")
        logger.info(f"  最大负差异: {diff.min():.6f}")
        
        return ultimate_sub, optimized_sub
        
    except FileNotFoundError as e:
        logger.error(f"找不到对比文件: {e}")
        return None, None

def diagnose_feature_quality():
    """诊断特征质量"""
    logger = logging.getLogger(__name__)
    logger.info("诊断特征质量问题")
    
    # 这里需要重新加载处理后的数据来分析
    # 由于数据处理过程复杂，我们先分析已知问题
    
    potential_issues = [
        "1. 特征选择可能过于激进，从1580个特征选择800个可能丢失了重要信息",
        "2. 高阶交互特征（687个）可能引入了噪声",
        "3. 时间窗口特征的大量缺失值填充可能引入偏差",
        "4. 聚类特征可能在测试集上表现不稳定",
        "5. 伪标签技术可能引入了错误标签",
        "6. 多模型集成的权重可能不合理"
    ]
    
    for issue in potential_issues:
        logger.warning(issue)

def create_simple_robust_model():
    """创建简单稳健的模型作为对比"""
    logger = logging.getLogger(__name__)
    logger.info("创建简单稳健模型")
    
    from data_loader import DataLoader
    from preprocessor import Preprocessor
    from feature_engineering import FeatureEngineer
    
    # 加载配置
    with open("config.yaml", "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)
    
    # 1. 加载数据
    data_loader = DataLoader(config)
    data_dict = data_loader.load_data()
    train_df, test_df = data_loader.get_train_test_data()
    
    # 2. 简单预处理
    preprocessor = Preprocessor()
    train_df_processed = preprocessor.preprocess(train_df, is_train=True)
    test_df_processed = preprocessor.preprocess(test_df, is_train=False)
    
    # 3. 基础特征工程（不使用高级特征）
    feature_engineer = FeatureEngineer(data_dict)
    train_df_featured = feature_engineer.engineer_features(train_df_processed)
    test_df_featured = feature_engineer.engineer_features(test_df_processed)
    
    # 4. 准备数据
    common_cols = [col for col in train_df_featured.columns if col in test_df_featured.columns]
    common_cols = [col for col in common_cols if col not in ["TARGET", "SK_ID_CURR"]]
    
    X = train_df_featured[common_cols]
    y = train_df_featured["TARGET"]
    test_X = test_df_featured[common_cols]
    test_ids = test_df_featured["SK_ID_CURR"]
    
    # 5. 数据清理
    for col in X.columns:
        if X[col].dtype == 'object':
            from sklearn.preprocessing import LabelEncoder
            le = LabelEncoder()
            combined = pd.concat([X[col], test_X[col]], axis=0)
            combined_encoded = le.fit_transform(combined.astype(str))
            X[col] = combined_encoded[:len(X)]
            test_X[col] = combined_encoded[len(X):]
    
    X = X.fillna(X.median())
    test_X = test_X.fillna(test_X.median())
    
    # 6. 训练简单模型
    model = LGBMClassifier(
        objective='binary',
        metric='auc',
        num_leaves=31,
        learning_rate=0.02,
        n_estimators=2000,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=0.1,
        verbose=-1,
        random_state=42
    )
    
    # 交叉验证
    cv_scores = cross_val_score(
        model, X, y,
        cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),
        scoring='roc_auc'
    )
    
    logger.info(f"简单模型交叉验证AUC: {cv_scores.mean():.6f} ± {cv_scores.std():.6f}")
    
    # 训练最终模型
    model.fit(X, y)
    test_predictions = model.predict_proba(test_X)[:, 1]
    
    # 生成提交文件
    simple_submission = pd.DataFrame({
        "SK_ID_CURR": test_ids,
        "TARGET": test_predictions
    })
    simple_submission.to_csv("simple_robust_submission.csv", index=False)
    
    logger.info("简单稳健模型提交文件已生成: simple_robust_submission.csv")
    logger.info(f"简单模型预测均值: {test_predictions.mean():.6f}")
    logger.info(f"简单模型预测标准差: {test_predictions.std():.6f}")
    
    return cv_scores.mean()

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始性能诊断")
    
    # 1. 加载原始数据
    train_df, test_df = load_data()
    
    # 2. 测试基线性能
    baseline_auc = test_baseline_performance(train_df)
    
    # 3. 分析提交文件
    submission = analyze_submission_file()
    
    # 4. 与优化版本对比
    ultimate_sub, optimized_sub = compare_with_optimized_submission()
    
    # 5. 诊断特征质量
    diagnose_feature_quality()
    
    # 6. 创建简单稳健模型
    simple_auc = create_simple_robust_model()
    
    # 7. 总结
    logger.info("=" * 50)
    logger.info("诊断总结:")
    logger.info(f"基线AUC (原始特征): {baseline_auc:.6f}")
    logger.info(f"简单模型AUC: {simple_auc:.6f}")
    logger.info("终极版本得分: 0.74 (远低于预期)")
    logger.info("优化版本得分: 0.789 (符合预期)")
    logger.info("=" * 50)
    
    # 8. 建议
    logger.info("改进建议:")
    logger.info("1. 减少特征工程的复杂度，避免过拟合")
    logger.info("2. 更保守的特征选择策略")
    logger.info("3. 简化模型集成，使用更少但更稳定的模型")
    logger.info("4. 避免使用伪标签技术")
    logger.info("5. 重点关注特征质量而非数量")

if __name__ == "__main__":
    main()
