"""
高级特征工程模块

实现更高级的特征工程技术，包括：
1. 时间序列特征（趋势、季节性、滞后特征）
2. 目标编码（Target Encoding）
3. 聚类特征
4. 更多交互特征
5. 统计特征（分位数、偏度、峰度）
6. 频率编码
7. 多项式特征

目标：将AUC从0.78提升到0.8+

作者：Augment Agent
"""

import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import PolynomialFeatures
from sklearn.model_selection import KFold
import logging
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class AdvancedFeatureEngineer:
    """高级特征工程器"""
    
    def __init__(self, data_dict, n_clusters=5):
        """
        初始化高级特征工程器
        
        参数:
            data_dict (dict): 包含所有数据集的字典
            n_clusters (int): 聚类数量
        """
        self.data = data_dict
        self.n_clusters = n_clusters
        self.target_encoders = {}
        self.cluster_models = {}
    
    def create_time_series_features(self, main_df):
        """创建时间序列特征"""
        logger.info("创建时间序列特征")
        
        # 从bureau_balance创建时间序列特征
        if "bureau_balance" in self.data and "bureau" in self.data:
            bureau = self.data["bureau"]
            bureau_balance = self.data["bureau_balance"]
            
            # 合并bureau和bureau_balance
            bb_merged = bureau_balance.merge(
                bureau[['SK_ID_BUREAU', 'SK_ID_CURR']], 
                on='SK_ID_BUREAU', 
                how='left'
            )
            
            # 按客户和月份排序
            bb_merged = bb_merged.sort_values(['SK_ID_CURR', 'MONTHS_BALANCE'])
            
            # 创建时间序列特征
            ts_features = []
            
            # 1. 趋势特征（最近3个月vs历史平均）
            bb_recent = bb_merged[bb_merged['MONTHS_BALANCE'] >= -3].groupby('SK_ID_CURR').agg({
                'MONTHS_BALANCE': ['count', 'mean']
            })
            bb_recent.columns = ['_'.join(col).strip() for col in bb_recent.columns.values]
            bb_recent.columns = ['RECENT_' + col for col in bb_recent.columns]
            ts_features.append(bb_recent)
            
            # 2. 状态变化特征
            bb_status = bb_merged.groupby('SK_ID_CURR')['STATUS'].agg([
                lambda x: (x == '0').sum(),  # 正常状态次数
                lambda x: (x != '0').sum(),  # 异常状态次数
                lambda x: len(x.unique()),   # 状态种类数
            ])
            bb_status.columns = ['STATUS_NORMAL_COUNT', 'STATUS_BAD_COUNT', 'STATUS_UNIQUE_COUNT']
            ts_features.append(bb_status)
            
            # 3. 最后状态特征
            bb_last_status = bb_merged.groupby('SK_ID_CURR').last()['STATUS']
            bb_last_status.name = 'LAST_STATUS'
            ts_features.append(bb_last_status.to_frame())
            
            # 合并所有时间序列特征
            for ts_feat in ts_features:
                main_df = main_df.merge(ts_feat, on='SK_ID_CURR', how='left')
        
        # 从installments_payments创建还款趋势特征
        if "installments_payments" in self.data:
            ins = self.data["installments_payments"]
            
            # 计算还款率趋势
            ins['PAYMENT_RATE'] = ins['AMT_PAYMENT'] / (ins['AMT_INSTALMENT'] + 1e-8)
            ins['DAYS_DIFF'] = ins['DAYS_ENTRY_PAYMENT'] - ins['DAYS_INSTALMENT']
            
            # 按时间排序，计算趋势
            ins_sorted = ins.sort_values(['SK_ID_CURR', 'DAYS_INSTALMENT'])
            
            # 最近几次还款的平均表现
            ins_recent_trend = ins_sorted.groupby('SK_ID_CURR').tail(5).groupby('SK_ID_CURR').agg({
                'PAYMENT_RATE': ['mean', 'std'],
                'DAYS_DIFF': ['mean', 'std']
            })
            ins_recent_trend.columns = ['_'.join(col).strip() for col in ins_recent_trend.columns.values]
            ins_recent_trend.columns = ['RECENT_' + col for col in ins_recent_trend.columns]
            
            main_df = main_df.merge(ins_recent_trend, on='SK_ID_CURR', how='left')
        
        logger.info("时间序列特征创建完成")
        return main_df
    
    def create_target_encoding_features(self, main_df, target_col='TARGET'):
        """创建目标编码特征"""
        logger.info("创建目标编码特征")
        
        if target_col not in main_df.columns:
            logger.warning("目标列不存在，跳过目标编码")
            return main_df
        
        # 选择要进行目标编码的分类特征
        categorical_features = [
            'NAME_CONTRACT_TYPE', 'CODE_GENDER', 'FLAG_OWN_CAR', 'FLAG_OWN_REALTY',
            'NAME_TYPE_SUITE', 'NAME_INCOME_TYPE', 'NAME_EDUCATION_TYPE', 
            'NAME_FAMILY_STATUS', 'NAME_HOUSING_TYPE', 'OCCUPATION_TYPE',
            'WEEKDAY_APPR_PROCESS_START', 'ORGANIZATION_TYPE'
        ]
        
        # 只保留存在的特征
        categorical_features = [f for f in categorical_features if f in main_df.columns]
        
        # 使用5折交叉验证进行目标编码，避免过拟合
        kf = KFold(n_splits=5, shuffle=True, random_state=42)
        
        for feature in categorical_features:
            logger.info(f"对特征 {feature} 进行目标编码")
            
            # 初始化编码结果
            target_encoded = np.zeros(len(main_df))
            
            for train_idx, val_idx in kf.split(main_df):
                # 计算训练集中每个类别的目标均值
                train_df = main_df.iloc[train_idx]
                target_means = train_df.groupby(feature)[target_col].mean()
                
                # 对验证集进行编码
                val_df = main_df.iloc[val_idx]
                target_encoded[val_idx] = val_df[feature].map(target_means).fillna(
                    train_df[target_col].mean()
                )
            
            # 添加目标编码特征
            main_df[f'{feature}_TARGET_ENCODED'] = target_encoded
        
        logger.info("目标编码特征创建完成")
        return main_df
    
    def create_clustering_features(self, main_df):
        """创建聚类特征"""
        logger.info("创建聚类特征")
        
        # 选择用于聚类的数值特征
        numeric_features = [
            'AMT_INCOME_TOTAL', 'AMT_CREDIT', 'AMT_ANNUITY', 'AMT_GOODS_PRICE',
            'DAYS_BIRTH', 'DAYS_EMPLOYED', 'DAYS_REGISTRATION', 'DAYS_ID_PUBLISH'
        ]
        
        # 只保留存在且非空的特征
        available_features = []
        for feat in numeric_features:
            if feat in main_df.columns and main_df[feat].notna().sum() > 1000:
                available_features.append(feat)
        
        if len(available_features) < 3:
            logger.warning("可用于聚类的特征太少，跳过聚类特征创建")
            return main_df
        
        # 准备聚类数据
        cluster_data = main_df[available_features].fillna(main_df[available_features].median())
        
        # 标准化
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        cluster_data_scaled = scaler.fit_transform(cluster_data)
        
        # 执行K-means聚类
        kmeans = KMeans(n_clusters=self.n_clusters, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(cluster_data_scaled)
        
        # 添加聚类标签
        main_df['CLUSTER_LABEL'] = clusters
        
        # 计算到聚类中心的距离
        distances = kmeans.transform(cluster_data_scaled)
        main_df['CLUSTER_DISTANCE_MIN'] = np.min(distances, axis=1)
        main_df['CLUSTER_DISTANCE_MEAN'] = np.mean(distances, axis=1)
        
        # 为每个聚类创建虚拟变量
        for i in range(self.n_clusters):
            main_df[f'CLUSTER_{i}'] = (clusters == i).astype(int)
        
        logger.info(f"聚类特征创建完成，创建了{self.n_clusters + 3}个特征")
        return main_df
    
    def create_frequency_encoding_features(self, main_df):
        """创建频率编码特征"""
        logger.info("创建频率编码特征")
        
        # 选择要进行频率编码的分类特征
        categorical_features = [
            'NAME_CONTRACT_TYPE', 'CODE_GENDER', 'NAME_INCOME_TYPE', 
            'NAME_EDUCATION_TYPE', 'NAME_FAMILY_STATUS', 'OCCUPATION_TYPE'
        ]
        
        # 只保留存在的特征
        categorical_features = [f for f in categorical_features if f in main_df.columns]
        
        for feature in categorical_features:
            # 计算频率
            freq_map = main_df[feature].value_counts().to_dict()
            main_df[f'{feature}_FREQ'] = main_df[feature].map(freq_map)
            
            # 计算频率占比
            main_df[f'{feature}_FREQ_RATIO'] = main_df[f'{feature}_FREQ'] / len(main_df)
        
        logger.info("频率编码特征创建完成")
        return main_df
    
    def create_statistical_features(self, main_df):
        """创建统计特征"""
        logger.info("创建统计特征")
        
        # 选择数值特征
        numeric_features = main_df.select_dtypes(include=[np.number]).columns
        numeric_features = [f for f in numeric_features if f not in ['SK_ID_CURR', 'TARGET']]
        
        # 创建特征组合的统计特征
        feature_groups = [
            ['AMT_INCOME_TOTAL', 'AMT_CREDIT', 'AMT_ANNUITY'],
            ['DAYS_BIRTH', 'DAYS_EMPLOYED', 'DAYS_REGISTRATION'],
            ['EXT_SOURCE_1', 'EXT_SOURCE_2', 'EXT_SOURCE_3']
        ]
        
        for i, group in enumerate(feature_groups):
            # 只保留存在的特征
            available_group = [f for f in group if f in main_df.columns]
            
            if len(available_group) >= 2:
                group_data = main_df[available_group]
                
                # 计算组内统计特征
                main_df[f'GROUP_{i}_MEAN'] = group_data.mean(axis=1)
                main_df[f'GROUP_{i}_STD'] = group_data.std(axis=1)
                main_df[f'GROUP_{i}_MIN'] = group_data.min(axis=1)
                main_df[f'GROUP_{i}_MAX'] = group_data.max(axis=1)
                main_df[f'GROUP_{i}_MEDIAN'] = group_data.median(axis=1)
                main_df[f'GROUP_{i}_SKEW'] = group_data.skew(axis=1)
        
        logger.info("统计特征创建完成")
        return main_df
    
    def create_advanced_interaction_features(self, main_df):
        """创建高级交互特征"""
        logger.info("创建高级交互特征")
        
        # 重要特征列表（扩展版）
        important_features = [
            'EXT_SOURCE_1', 'EXT_SOURCE_2', 'EXT_SOURCE_3',
            'AMT_CREDIT', 'AMT_INCOME_TOTAL', 'AMT_ANNUITY',
            'DAYS_BIRTH', 'DAYS_EMPLOYED', 'AMT_GOODS_PRICE'
        ]
        
        # 只保留存在的特征
        available_features = [f for f in important_features if f in main_df.columns]
        
        # 创建更多交互特征
        new_features = {}
        
        # 1. 三元交互特征
        if len(available_features) >= 3:
            for i in range(len(available_features)):
                for j in range(i+1, len(available_features)):
                    for k in range(j+1, len(available_features)):
                        feat1, feat2, feat3 = available_features[i], available_features[j], available_features[k]
                        
                        # 三元乘积
                        new_features[f'{feat1}_X_{feat2}_X_{feat3}'] = (
                            main_df[feat1] * main_df[feat2] * main_df[feat3]
                        )
        
        # 2. 条件特征（基于分位数）
        for feat in available_features:
            if main_df[feat].notna().sum() > 1000:
                q25 = main_df[feat].quantile(0.25)
                q75 = main_df[feat].quantile(0.75)
                
                new_features[f'{feat}_IS_LOW'] = (main_df[feat] <= q25).astype(int)
                new_features[f'{feat}_IS_HIGH'] = (main_df[feat] >= q75).astype(int)
        
        # 3. 比率特征的高级组合
        if all(f in main_df.columns for f in ['AMT_CREDIT', 'AMT_INCOME_TOTAL', 'AMT_ANNUITY']):
            new_features['CREDIT_INCOME_ANNUITY_RATIO'] = (
                main_df['AMT_CREDIT'] / (main_df['AMT_INCOME_TOTAL'] + main_df['AMT_ANNUITY'] + 1e-8)
            )
        
        # 将所有新特征转换为DataFrame并连接
        if new_features:
            new_features_df = pd.DataFrame(new_features, index=main_df.index)
            # 处理无穷大值
            new_features_df = new_features_df.replace([np.inf, -np.inf], np.nan)
            # 连接新特征
            main_df = pd.concat([main_df, new_features_df], axis=1)
        
        logger.info(f"高级交互特征创建完成，添加了{len(new_features)}个特征")
        return main_df
    
    def engineer_advanced_features(self, main_df):
        """执行所有高级特征工程步骤"""
        logger.info("开始高级特征工程")
        
        original_features = main_df.shape[1]
        
        # 1. 时间序列特征
        main_df = self.create_time_series_features(main_df)
        
        # 2. 目标编码特征（仅对训练集）
        if 'TARGET' in main_df.columns:
            main_df = self.create_target_encoding_features(main_df)
        
        # 3. 聚类特征
        main_df = self.create_clustering_features(main_df)
        
        # 4. 频率编码特征
        main_df = self.create_frequency_encoding_features(main_df)
        
        # 5. 统计特征
        main_df = self.create_statistical_features(main_df)
        
        # 6. 高级交互特征
        main_df = self.create_advanced_interaction_features(main_df)
        
        new_features = main_df.shape[1] - original_features
        logger.info(f"高级特征工程完成，新增{new_features}个特征，总特征数: {main_df.shape[1]}")
        
        return main_df
