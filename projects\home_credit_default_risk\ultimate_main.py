"""
Home Credit Default Risk - 终极优化版主程序

整合所有最先进的技术，目标AUC 0.8+：
1. 大规模特征工程（1000+特征）
2. 多模型集成（10+模型）
3. 多层堆叠和贝叶斯融合
4. 伪标签技术
5. 特征选择优化

基于0.789的成功经验，进行全面升级

作者：Augment Agent
"""

import pandas as pd
import numpy as np
import yaml
import logging
import os
from datetime import datetime
from sklearn.metrics import roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif

from data_loader import DataLoader
from preprocessor import Preprocessor
from feature_engineering import FeatureEngineer
from credit_specific_features import CreditSpecificFeatureEngineer
from advanced_feature_factory import AdvancedFeatureFactory
from multi_model_ensemble import MultiModelEnsemble


def setup_logging():
    """设置日志记录"""
    if not os.path.exists("logs"):
        os.makedirs("logs")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"logs/ultimate_pipeline_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ],
    )

    return logging.getLogger(__name__)


def advanced_feature_selection(X, y, n_features=800):
    """高级特征选择"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始特征选择，从{X.shape[1]}个特征中选择{n_features}个")

    # 确保没有NaN值
    X_clean = X.fillna(0)

    # 1. 基于F统计量的特征选择
    try:
        f_selector = SelectKBest(score_func=f_classif, k=min(n_features*2, X.shape[1]))
        X_f_selected = f_selector.fit_transform(X_clean, y)
        f_selected_features = X.columns[f_selector.get_support()].tolist()
    except Exception as e:
        logger.warning(f"F统计量特征选择失败: {e}，使用前{n_features}个特征")
        f_selected_features = X.columns[:n_features].tolist()

    # 2. 基于互信息的特征选择
    try:
        mi_selector = SelectKBest(score_func=mutual_info_classif, k=min(n_features*2, X.shape[1]))
        X_mi_selected = mi_selector.fit_transform(X_clean, y)
        mi_selected_features = X.columns[mi_selector.get_support()].tolist()
    except Exception as e:
        logger.warning(f"互信息特征选择失败: {e}，使用F统计量结果")
        mi_selected_features = f_selected_features

    # 3. 合并两种方法的结果
    combined_features = list(set(f_selected_features + mi_selected_features))

    # 4. 如果特征数量仍然太多，使用更严格的选择
    if len(combined_features) > n_features:
        try:
            final_selector = SelectKBest(score_func=f_classif, k=n_features)
            final_selector.fit(X_clean[combined_features], y)
            final_features = [combined_features[i] for i in range(len(combined_features)) if final_selector.get_support()[i]]
        except Exception as e:
            logger.warning(f"最终特征选择失败: {e}，使用前{n_features}个特征")
            final_features = combined_features[:n_features]
    else:
        final_features = combined_features

    logger.info(f"特征选择完成，最终选择{len(final_features)}个特征")
    return X[final_features], final_features


def implement_pseudo_labeling(X, y, test_X, test_ids, confidence_threshold=0.95):
    """实现伪标签技术"""
    logger = logging.getLogger(__name__)
    logger.info("开始伪标签技术")
    
    # 先用部分数据训练一个快速模型
    from lightgbm import LGBMClassifier
    
    quick_model = LGBMClassifier(
        objective='binary',
        metric='auc',
        num_leaves=31,
        learning_rate=0.1,
        n_estimators=500,
        verbose=-1,
        random_state=42
    )
    
    # 使用部分训练数据训练快速模型
    sample_size = min(50000, len(X))
    sample_idx = np.random.choice(len(X), sample_size, replace=False)
    X_sample = X.iloc[sample_idx]
    y_sample = y.iloc[sample_idx]
    
    quick_model.fit(X_sample, y_sample)
    
    # 对测试集进行预测
    test_proba = quick_model.predict_proba(test_X)[:, 1]
    
    # 选择高置信度样本
    high_conf_mask = (test_proba >= confidence_threshold) | (test_proba <= (1 - confidence_threshold))
    high_conf_indices = np.where(high_conf_mask)[0]
    
    if len(high_conf_indices) > 0:
        # 创建伪标签
        pseudo_labels = (test_proba[high_conf_indices] >= 0.5).astype(int)
        
        # 添加到训练集
        X_pseudo = test_X.iloc[high_conf_indices]
        y_pseudo = pd.Series(pseudo_labels, index=X_pseudo.index)
        
        # 合并数据
        X_augmented = pd.concat([X, X_pseudo], axis=0, ignore_index=True)
        y_augmented = pd.concat([y, y_pseudo], axis=0, ignore_index=True)
        
        logger.info(f"伪标签技术完成，添加了{len(high_conf_indices)}个高置信度样本")
        logger.info(f"其中正样本{sum(pseudo_labels)}个，负样本{len(pseudo_labels)-sum(pseudo_labels)}个")
        
        return X_augmented, y_augmented
    else:
        logger.warning("没有找到足够高置信度的样本，跳过伪标签")
        return X, y


def clean_and_prepare_data(X, test_X, logger):
    """数据清理和准备"""
    logger.info("开始最终数据清理")
    
    # 处理分类列
    for col in X.columns:
        if X[col].dtype == 'object' or str(X[col].dtype).startswith('category'):
            logger.info(f"处理分类列: {col}")
            from sklearn.preprocessing import LabelEncoder
            le = LabelEncoder()
            
            combined = pd.concat([X[col], test_X[col]], axis=0)
            combined_encoded = le.fit_transform(combined.astype(str))
            
            X[col] = combined_encoded[:len(X)]
            test_X[col] = combined_encoded[len(X):]
    
    # 处理无穷大值
    X = X.replace([np.inf, -np.inf], np.nan)
    test_X = test_X.replace([np.inf, -np.inf], np.nan)
    
    # 处理极值
    for col in X.select_dtypes(include=[np.number]).columns:
        q01 = X[col].quantile(0.001)
        q99 = X[col].quantile(0.999)
        
        if pd.notna(q01) and pd.notna(q99) and q01 != q99:
            X[col] = X[col].clip(lower=q01, upper=q99)
            test_X[col] = test_X[col].clip(lower=q01, upper=q99)
    
    # 智能填充缺失值
    logger.info("开始填充缺失值")
    for col in X.columns:
        if X[col].isnull().sum() > 0:
            logger.info(f"填充列 {col}，缺失值数量: {X[col].isnull().sum()}")
            if X[col].dtype in ['int64', 'float64']:
                # 对于数值列，使用中位数填充
                fill_value = X[col].median()
                if pd.isna(fill_value):  # 如果中位数也是NaN，使用0
                    fill_value = 0
                X[col] = X[col].fillna(fill_value)
                test_X[col] = test_X[col].fillna(fill_value)
            else:
                # 对于分类列，使用众数或默认值
                mode_values = X[col].mode()
                if len(mode_values) > 0 and pd.notna(mode_values.iloc[0]):
                    fill_value = mode_values.iloc[0]
                else:
                    fill_value = 'Unknown'  # 默认值
                X[col] = X[col].fillna(fill_value)
                test_X[col] = test_X[col].fillna(fill_value)

    # 最终强制填充任何剩余的NaN
    logger.info("最终NaN检查和填充")
    for col in X.columns:
        if X[col].isnull().sum() > 0:
            logger.warning(f"强制填充剩余NaN: {col}")
            if X[col].dtype in ['int64', 'float64']:
                X[col] = X[col].fillna(0)
                test_X[col] = test_X[col].fillna(0)
            else:
                X[col] = X[col].fillna('Unknown')
                test_X[col] = test_X[col].fillna('Unknown')
    
    # 最终检查
    logger.info(f"数据清理完成: {X.shape}")
    
    # 确保没有问题
    assert not X.isnull().any().any(), "训练集仍有NaN值"
    assert not test_X.isnull().any().any(), "测试集仍有NaN值"
    
    numeric_cols = X.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        assert not np.isinf(X[numeric_cols].values).any(), "训练集仍有无穷大值"
        assert not np.isinf(test_X[numeric_cols].values).any(), "测试集仍有无穷大值"
    
    return X, test_X


def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始执行终极优化版Home Credit Default Risk Pipeline")
    logger.info("目标：AUC 0.8+")

    try:
        # 加载配置
        with open("config.yaml", "r", encoding="utf-8") as file:
            config = yaml.safe_load(file)

        # 1. 加载数据
        logger.info("步骤1: 加载数据")
        data_loader = DataLoader(config)
        data_dict = data_loader.load_data()
        train_df, test_df = data_loader.get_train_test_data()

        # 2. 数据预处理
        logger.info("步骤2: 数据预处理")
        preprocessor = Preprocessor()
        train_df_processed = preprocessor.preprocess(train_df, is_train=True)
        test_df_processed = preprocessor.preprocess(test_df, is_train=False)

        # 3. 基础特征工程
        logger.info("步骤3: 基础特征工程")
        feature_engineer = FeatureEngineer(data_dict)
        train_df_featured = feature_engineer.engineer_features(train_df_processed)
        test_df_featured = feature_engineer.engineer_features(test_df_processed)

        # 4. Home Credit专用特征工程
        logger.info("步骤4: Home Credit专用特征工程")
        credit_engineer = CreditSpecificFeatureEngineer(data_dict)
        train_df_enhanced = credit_engineer.engineer_all_features(train_df_featured)
        test_df_enhanced = credit_engineer.engineer_all_features(test_df_featured)

        # 5. 大规模高级特征工程
        logger.info("步骤5: 大规模高级特征工程")
        advanced_factory = AdvancedFeatureFactory(data_dict)
        train_df_ultimate = advanced_factory.create_all_advanced_features(train_df_enhanced)
        test_df_ultimate = advanced_factory.create_all_advanced_features(test_df_enhanced)

        # 6. 准备最终数据
        logger.info("步骤6: 准备最终数据")
        common_cols = [
            col for col in train_df_ultimate.columns if col in test_df_ultimate.columns
        ]
        common_cols = [col for col in common_cols if col not in ["TARGET", "SK_ID_CURR"]]

        logger.info(f"总特征数量: {len(common_cols)}")

        X = train_df_ultimate[common_cols]
        y = train_df_ultimate["TARGET"]
        test_X = test_df_ultimate[common_cols]
        test_ids = test_df_ultimate["SK_ID_CURR"]

        # 清理特征名
        def clean_feature_names(df):
            df = df.copy()
            import re
            df.columns = [re.sub(r"[^a-zA-Z0-9_]", "_", col) for col in df.columns]
            return df

        X = clean_feature_names(X)
        test_X = clean_feature_names(test_X)

        # 数据清理
        X, test_X = clean_and_prepare_data(X, test_X, logger)

        # 7. 特征选择
        logger.info("步骤7: 智能特征选择")
        X_selected, selected_features = advanced_feature_selection(X, y, n_features=800)
        test_X_selected = test_X[selected_features]

        # 8. 伪标签技术
        logger.info("步骤8: 伪标签技术")
        X_augmented, y_augmented = implement_pseudo_labeling(
            X_selected, y, test_X_selected, test_ids, confidence_threshold=0.98
        )

        # 9. 多模型集成训练
        logger.info("步骤9: 多模型集成训练")
        ensemble = MultiModelEnsemble(n_folds=5, random_state=42)
        final_oof, final_test, ensemble_results = ensemble.ensemble_all_methods(
            X_augmented, y_augmented, test_X_selected
        )

        # 10. 生成提交文件
        logger.info("步骤10: 生成提交文件")
        submission = pd.DataFrame({
            "SK_ID_CURR": test_ids,
            "TARGET": final_test
        })
        submission.to_csv("ultimate_submission.csv", index=False)

        # 输出最终结果
        final_auc = roc_auc_score(y, final_oof[:len(y)])  # 只使用原始训练集计算AUC
        logger.info(f"最终交叉验证AUC: {final_auc:.6f}")

        # 显示特征重要性
        if ensemble.feature_importances is not None:
            top_features = ensemble.feature_importances.head(50)
            logger.info("Top 50 重要特征:")
            for feature, importance in top_features.items():
                logger.info(f"  {feature}: {importance:.6f}")

        logger.info("终极优化Pipeline执行完成")
        logger.info(f"提交文件已保存到: ultimate_submission.csv")
        logger.info(f"性能提升: 从0.78到{final_auc:.6f} (+{final_auc-0.78:.6f})")

    except Exception as e:
        logger.error(f"Pipeline执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    main()
